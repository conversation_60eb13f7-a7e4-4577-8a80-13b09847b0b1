{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-flight-data.ts"], "names": ["CacheStates", "fillLazyItemsTillLeafWithHead", "fillCacheWithNewSubTreeData", "applyFlightData", "existingCache", "cache", "flightDataPath", "wasPrefetched", "treePatch", "cacheNodeSeedData", "head", "slice", "length", "subTreeData", "status", "READY", "parallelRoutes", "Map"], "mappings": "AAAA,SAASA,WAAW,QAAQ,wDAAuD;AAGnF,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,2BAA2B,QAAQ,qCAAoC;AAEhF,OAAO,SAASC,gBACdC,aAAwB,EACxBC,KAAgB,EAChBC,cAA8B,EAC9BC,aAA8B;IAA9BA,IAAAA,0BAAAA,gBAAyB;IAEzB,0DAA0D;IAC1D,MAAM,CAACC,WAAWC,mBAAmBC,KAAK,GAAGJ,eAAeK,KAAK,CAAC,CAAC;IAEnE,8FAA8F;IAC9F,IAAIF,sBAAsB,MAAM;QAC9B,OAAO;IACT;IAEA,IAAIH,eAAeM,MAAM,KAAK,GAAG;QAC/B,MAAMC,cAAcJ,iBAAiB,CAAC,EAAE;QACxCJ,MAAMS,MAAM,GAAGd,YAAYe,KAAK;QAChCV,MAAMQ,WAAW,GAAGA;QACpBZ,8BACEI,OACAD,eACAI,WACAC,mBACAC,MACAH;IAEJ,OAAO;QACL,mDAAmD;QACnDF,MAAMS,MAAM,GAAGd,YAAYe,KAAK;QAChCV,MAAMQ,WAAW,GAAGT,cAAcS,WAAW;QAC7CR,MAAMW,cAAc,GAAG,IAAIC,IAAIb,cAAcY,cAAc;QAC3D,oEAAoE;QACpEd,4BACEG,OACAD,eACAE,gBACAC;IAEJ;IAEA,OAAO;AACT"}