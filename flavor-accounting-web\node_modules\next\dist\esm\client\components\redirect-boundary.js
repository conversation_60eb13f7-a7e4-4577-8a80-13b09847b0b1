"use client";

import React, { useEffect } from "react";
import { useRouter } from "./navigation";
import { RedirectType, getRedirectTypeFromError, getURLFromRedirectError, isRedirectError } from "./redirect";
function HandleRedirect(param) {
    let { redirect, reset, redirectType } = param;
    const router = useRouter();
    useEffect(()=>{
        React.startTransition(()=>{
            if (redirectType === RedirectType.push) {
                router.push(redirect, {});
            } else {
                router.replace(redirect, {});
            }
            reset();
        });
    }, [
        redirect,
        redirectType,
        reset,
        router
    ]);
    return null;
}
export class RedirectErrorBoundary extends React.Component {
    static getDerivedStateFromError(error) {
        if (isRedirectError(error)) {
            const url = getURLFromRedirectError(error);
            const redirectType = getRedirectTypeFromError(error);
            return {
                redirect: url,
                redirectType
            };
        }
        // Re-throw if error is not for redirect
        throw error;
    }
    render() {
        const { redirect, redirectType } = this.state;
        if (redirect !== null && redirectType !== null) {
            return /*#__PURE__*/ React.createElement(HandleRedirect, {
                redirect: redirect,
                redirectType: redirectType,
                reset: ()=>this.setState({
                        redirect: null
                    })
            });
        }
        return this.props.children;
    }
    constructor(props){
        super(props);
        this.state = {
            redirect: null,
            redirectType: null
        };
    }
}
export function RedirectBoundary(param) {
    let { children } = param;
    const router = useRouter();
    return /*#__PURE__*/ React.createElement(RedirectErrorBoundary, {
        router: router
    }, children);
}

//# sourceMappingURL=redirect-boundary.js.map