/*
 React
 react-server-dom-webpack-server.browser.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("react-dom"),ba=require("react"),l=null,n=0;function p(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<n&&(a.enqueue(new Uint8Array(l.buffer,0,n)),l=new Uint8Array(512),n=0),a.enqueue(b);else{var d=l.length-n;d<b.byteLength&&(0===d?a.enqueue(l):(l.set(b.subarray(0,d),n),a.enqueue(l),b=b.subarray(d)),l=new Uint8Array(512),n=0);l.set(b,n);n+=b.byteLength}return!0}var q=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),t=Symbol.for("react.server.reference");function u(a,b,d){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:d}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===t){var b=ea.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:fa}})}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ja(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "__esModule":var d=a.$$id;a.default=u(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=u({},a.$$id,!0),e=new Proxy(c,ka);a.status="fulfilled";a.value=e;return a.then=u(function(g){return Promise.resolve(g(e))},a.$$id+"#then",!1)}c=a[b];c||(c=u(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ia));return c}
var ka={get:function(a,b){return ja(a,b)},getOwnPropertyDescriptor:function(a,b){var d=Object.getOwnPropertyDescriptor(a,b);d||(d={value:ja(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,d));return d},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");}},sa={prefetchDNS:la,preconnect:ma,preload:na,preloadModule:oa,preinitStyle:pa,preinitScript:qa,preinitModuleScript:ra};
function la(a){if("string"===typeof a&&a){var b=v?v:null;if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),w(b,"D",a))}}}function ma(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?w(d,"C",[a,b]):w(d,"C",a))}}}
function na(a,b,d){if("string"===typeof a){var c=v?v:null;if(c){var e=c.hints,g="L";if("image"===b&&d){var f=d.imageSrcSet,k=d.imageSizes,h="";"string"===typeof f&&""!==f?(h+="["+f+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;g+="[image]"+h}else g+="["+b+"]"+a;e.has(g)||(e.add(g),(d=y(d))?w(c,"L",[a,b,d]):w(c,"L",[a,b]))}}}function oa(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=y(b))?w(d,"m",[a,b]):w(d,"m",a)}}}
function pa(a,b,d){if("string"===typeof a){var c=v?v:null;if(c){var e=c.hints,g="S|"+a;if(!e.has(g))return e.add(g),(d=y(d))?w(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?w(c,"S",[a,b]):w(c,"S",a)}}}function qa(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=y(b))?w(d,"X",[a,b]):w(d,"X",a)}}}
function ra(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=y(b))?w(d,"M",[a,b]):w(d,"M",a)}}}function y(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var ta=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,z=Symbol.for("react.element"),ua=Symbol.for("react.fragment"),va=Symbol.for("react.server_context"),wa=Symbol.for("react.forward_ref"),xa=Symbol.for("react.suspense"),ya=Symbol.for("react.suspense_list"),za=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),Aa=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.postpone");var Ba=Symbol.iterator,B=null;
function C(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");C(a,d);b.context._currentValue=b.value}}}function Ca(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ca(a)}
function Da(a){var b=a.parent;null!==b&&Da(b);a.context._currentValue=a.value}function Ea(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?C(a,b):Ea(a,b)}
function Fa(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?C(a,d):Fa(a,d);b.context._currentValue=b.value}var Ga=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Ha(){}function Ia(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Ha,Ha),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}D=b;throw Ga;}}var D=null;
function Ja(){if(null===D)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=D;D=null;return a}var E=null,F=0,G=null;function Ka(){var a=G;G=null;return a}function La(a){return a._currentValue}
var Pa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:H,useTransition:H,readContext:La,useContext:La,useReducer:H,useRef:H,useState:H,useInsertionEffect:H,useLayoutEffect:H,useImperativeHandle:H,useEffect:H,useId:Ma,useSyncExternalStore:H,useCacheRefresh:function(){return Na},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Aa;return b},use:Oa};
function H(){throw Error("This Hook is not supported in Server Components.");}function Na(){throw Error("Refreshing the cache is not supported in Server Components.");}function Ma(){if(null===E)throw Error("useId can only be used while React is rendering");var a=E.identifierCount++;return":"+E.identifierPrefix+"S"+a.toString(32)+":"}
function Oa(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=F;F+=1;null===G&&(G=[]);return Ia(G,a,b)}if(a.$$typeof===va)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Qa(){return(new AbortController).signal}function Ra(){var a=v?v:null;return a?a.cache:new Map}
var Sa={getCacheSignal:function(){var a=Ra(),b=a.get(Qa);void 0===b&&(b=Qa(),a.set(Qa,b));return b},getCacheForType:function(a){var b=Ra(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},Ta=Array.isArray,Ua=Object.getPrototypeOf;function Va(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function Wa(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Ta(a))return"[...]";a=Va(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function I(a){if("string"===typeof a)return a;switch(a){case xa:return"Suspense";case ya:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case wa:return I(a.render);case za:return I(a.type);case A:var b=a._payload;a=a._init;try{return I(a(b))}catch(d){}}return""}
function J(a,b){var d=Va(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(Ta(a)){var e="[";for(var g=0;g<a.length;g++){0<g&&(e+=", ");var f=a[g];f="object"===typeof f&&null!==f?J(f):Wa(f);""+g===b?(d=e.length,c=f.length,e+=f):e=10>f.length&&40>e.length+f.length?e+f:e+"..."}e+="]"}else if(a.$$typeof===z)e="<"+I(a.type)+"/>";else{e="{";g=Object.keys(a);for(f=0;f<g.length;f++){0<f&&(e+=", ");var k=g[f],h=JSON.stringify(k);e+=('"'+k+'"'===h?k:h)+": ";h=a[k];h="object"===typeof h&&null!==h?J(h):
Wa(h);k===b?(d=e.length,c=h.length,e+=h):e=10>h.length&&40>e.length+h.length?e+h:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var Xa=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ya=ba.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;if(!Ya)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');
var Za=Object.prototype,K=JSON.stringify,$a=Ya.ReactCurrentCache,ab=Xa.ReactCurrentDispatcher;function bb(a){console.error(a)}function cb(){}
function db(a,b,d,c,e,g){if(null!==$a.current&&$a.current!==Sa)throw Error("Currently React only supports one RSC renderer at a time.");ta.current=sa;$a.current=Sa;var f=new Set;c=[];var k=new Set,h={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:k,abortableTasks:f,pingedTasks:c,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:[],onError:void 0===d?bb:d,onPostpone:void 0===g?cb:g,toJSON:function(m,x){return eb(h,this,m,x)}};h.pendingChunks++;a=L(h,a,null,f);c.push(a);return h}var v=null;
function fb(a,b){a.pendingChunks++;var d=L(a,null,B,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,gb(a,d),d.id;case "rejected":var c=M(a,b.reason);N(a,d.id,c);return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=e;gb(a,d)},function(e){d.status=4;a.abortableTasks.delete(d);e=M(a,e);N(a,
d.id,e);null!==a.destination&&O(a,a.destination)});return d.id}function w(a,b,d){d=K(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;d=q.encode(b+d+"\n");a.completedHintChunks.push(d);!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(d=a.destination,a.flushScheduled=!0,O(a,d))}function hb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function ib(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:A,_payload:a,_init:hb}}
function P(a,b,d,c,e,g){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===r)return[z,b,d,e];F=0;G=g;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:ib(e):e}if("string"===typeof b)return[z,b,d,e];if("symbol"===typeof b)return b===ua?e.children:[z,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===r)return[z,b,d,e];switch(b.$$typeof){case A:var f=
b._init;b=f(b._payload);return P(a,b,d,c,e,g);case wa:return a=b.render,F=0,G=g,a(e,void 0);case za:return P(a,b.type,d,c,e,g)}}throw Error("Unsupported Server Component type: "+Wa(b));}function gb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,jb(a))}function L(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return gb(a,e)},thenableState:null};c.add(e);return e}function Q(a){return"$"+a.toString(16)}
function kb(a,b,d){a=K(d);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function lb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,g=a.writtenClientReferences,f=g.get(e);if(void 0!==f)return b[0]===z&&"1"===d?"$L"+f.toString(16):Q(f);try{var k=a.bundlerConfig,h=c.$$id;f="";var m=k[h];if(m)f=m.name;else{var x=h.lastIndexOf("#");-1!==x&&(f=h.slice(x+1),m=k[h.slice(0,x)]);if(!m)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var Db=!0===c.$$async?[m.id,m.chunks,f,1]:[m.id,m.chunks,
f];a.pendingChunks++;var X=a.nextChunkId++,Eb=K(Db),Fb=X.toString(16)+":I"+Eb+"\n",Gb=q.encode(Fb);a.completedImportChunks.push(Gb);g.set(e,X);return b[0]===z&&"1"===d?"$L"+X.toString(16):Q(X)}catch(Hb){return a.pendingChunks++,b=a.nextChunkId++,d=M(a,Hb),N(a,b,d),Q(b)}}function R(a,b){a.pendingChunks++;b=L(a,b,B,a.abortableTasks);mb(a,b);return b.id}var S=!1;
function eb(a,b,d,c){switch(c){case z:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===z||c.$$typeof===A);)try{switch(c.$$typeof){case z:var e=a.writtenObjects,g=e.get(c);if(void 0!==g){if(-1===g){var f=R(a,c);return Q(f)}if(S===c)S=null;else return Q(g)}else e.set(c,-1);var k=c;c=P(a,k.type,k.key,k.ref,k.props,null);break;case A:var h=c._init;c=h(c._payload)}}catch(m){b=m===Ga?Ja():m;if("object"===typeof b&&null!==b&&"function"===typeof b.then)return a.pendingChunks++,a=L(a,c,B,a.abortableTasks),
c=a.ping,b.then(c,c),a.thenableState=Ka(),"$L"+a.id.toString(16);a.pendingChunks++;c=a.nextChunkId++;b=M(a,b);N(a,c,b);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){if(c.$$typeof===r)return lb(a,b,d,c);b=a.writtenObjects;d=b.get(c);if("function"===typeof c.then){if(void 0!==d)if(S===c)S=null;else return"$@"+d.toString(16);a=fb(a,c);b.set(c,a);return"$@"+a.toString(16)}if(void 0!==d){if(-1===d)return a=R(a,c),Q(a);if(S===c)S=null;else return Q(d)}else b.set(c,-1);if(Ta(c))return c;
if(c instanceof Map){c=Array.from(c);for(b=0;b<c.length;b++)d=c[b][0],"object"===typeof d&&null!==d&&(e=a.writtenObjects,void 0===e.get(d)&&e.set(d,-1));return"$Q"+R(a,c).toString(16)}if(c instanceof Set){c=Array.from(c);for(b=0;b<c.length;b++)d=c[b],"object"===typeof d&&null!==d&&(e=a.writtenObjects,void 0===e.get(d)&&e.set(d,-1));return"$W"+R(a,c).toString(16)}null===c||"object"!==typeof c?a=null:(a=Ba&&c[Ba]||c["@@iterator"],a="function"===typeof a?a:null);if(a)return Array.from(c);a=Ua(c);if(a!==
Za&&(null===a||null!==Ua(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return c}if("string"===typeof c){if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,b=a.nextChunkId++,c=q.encode(c),d=c.byteLength,d=b.toString(16)+":T"+d.toString(16)+",",d=q.encode(d),a.completedRegularChunks.push(d,c),Q(b);a="$"===c[0]?"$"+c:c;return a}if("boolean"===
typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){if(c.$$typeof===r)return lb(a,b,d,c);if(c.$$typeof===t)return b=a.writtenServerReferences,d=b.get(c),void 0!==d?a="$F"+d.toString(16):(d=c.$$bound,d={id:c.$$id,bound:d?Promise.resolve(d):null},a=R(a,d),b.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+
J(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+J(b,d));}if("symbol"===typeof c){e=a.writtenSymbols;g=e.get(c);if(void 0!==g)return Q(g);g=c.description;if(Symbol.for(g)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+
J(b,d));a.pendingChunks++;b=a.nextChunkId++;d=kb(a,b,"$S"+g);a.completedImportChunks.push(d);e.set(c,b);return Q(b)}if("bigint"===typeof c)return"$n"+c.toString(10);throw Error("Type "+typeof c+" is not supported in Client Component props."+J(b,d));}
function M(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}function nb(a,b){null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}
function N(a,b,d){d={digest:d};b=b.toString(16)+":E"+K(d)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}
function mb(a,b){if(0===b.status){var d=B,c=b.context;d!==c&&(null===d?Da(c):null===c?Ca(d):d.depth===c.depth?C(d,c):d.depth>c.depth?Ea(d,c):Fa(d,c),B=c);try{var e=b.model;if("object"===typeof e&&null!==e&&e.$$typeof===z){a.writtenObjects.set(e,b.id);d=e;var g=b.thenableState;b.model=e;e=P(a,d.type,d.key,d.ref,d.props,g);for(b.thenableState=null;"object"===typeof e&&null!==e&&e.$$typeof===z;)a.writtenObjects.set(e,b.id),g=e,b.model=e,e=P(a,g.type,g.key,g.ref,g.props,null)}"object"===typeof e&&null!==
e&&a.writtenObjects.set(e,b.id);var f=b.id;S=e;var k=K(e,a.toJSON),h=f.toString(16)+":"+k+"\n",m=q.encode(h);a.completedRegularChunks.push(m);a.abortableTasks.delete(b);b.status=1}catch(x){f=x===Ga?Ja():x,"object"===typeof f&&null!==f&&"function"===typeof f.then?(a=b.ping,f.then(a,a),b.thenableState=Ka()):(a.abortableTasks.delete(b),b.status=4,f=M(a,f),N(a,b.id,f))}}}
function jb(a){var b=ab.current;ab.current=Pa;var d=v;E=v=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++)mb(a,c[e]);null!==a.destination&&O(a,a.destination)}catch(g){M(a,g),nb(a,g)}finally{ab.current=b,E=null,v=d}}
function O(a,b){l=new Uint8Array(512);n=0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)a.pendingChunks--,p(b,d[c]);d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)p(b,e[c]);e.splice(0,c);var g=a.completedRegularChunks;for(c=0;c<g.length;c++)a.pendingChunks--,p(b,g[c]);g.splice(0,c);var f=a.completedErrorChunks;for(c=0;c<f.length;c++)a.pendingChunks--,p(b,f[c]);f.splice(0,c)}finally{a.flushScheduled=!1,l&&0<n&&(b.enqueue(new Uint8Array(l.buffer,0,n)),l=null,n=0)}0===a.pendingChunks&&
b.close()}function ob(a,b){try{var d=a.abortableTasks;if(0<d.size){a.pendingChunks++;var c=a.nextChunkId++,e=void 0===b?Error("The render was aborted by the server without a reason."):b,g=M(a,e);N(a,c,g,e);d.forEach(function(f){f.status=3;var k=Q(c);f=kb(a,f.id,k);a.completedErrorChunks.push(f)});d.clear()}null!==a.destination&&O(a,a.destination)}catch(f){M(a,f),nb(a,f)}}
function pb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[c.id,c.chunks,d]}var T=new Map;
function qb(a){var b=__webpack_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function rb(){}
function sb(a){for(var b=a[1],d=[],c=0;c<b.length;){var e=b[c++],g=b[c++],f=T.get(e);void 0===f?(tb.set(e,g),g=__webpack_chunk_load__(e),d.push(g),f=T.set.bind(T,e,null),g.then(f,rb),T.set(e,g)):null!==f&&d.push(f)}return 4===a.length?0===d.length?qb(a[0]):Promise.all(d).then(function(){return qb(a[0])}):0<d.length?Promise.all(d):null}
function U(a){var b=__webpack_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}var tb=new Map,ub=__webpack_require__.u;__webpack_require__.u=function(a){var b=tb.get(a);return void 0!==b?b:ub(a)};function V(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}V.prototype=Object.create(Promise.prototype);
V.prototype.then=function(a,b){switch(this.status){case "resolved_model":vb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function wb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function xb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&wb(d,b)}}function yb(a,b,d,c,e,g){var f=pb(a._bundlerConfig,b);a=sb(f);if(d)d=Promise.all([d,a]).then(function(k){k=k[0];var h=U(f);return h.bind.apply(h,[null].concat(k))});else if(a)d=Promise.resolve(a).then(function(){return U(f)});else return U(f);d.then(zb(c,e,g),Ab(c));return null}var W=null,Y=null;
function vb(a){var b=W,d=Y;W=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{W=b,Y=d}}function Bb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&xb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new V("resolved_model",c,null,a):new V("pending",null,null,a),d.set(b,c));return c}function zb(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&wb(e,c.value))}}function Ab(a){return function(b){return xb(a,b)}}
function Cb(a,b){a=Z(a,b);"resolved_model"===a.status&&vb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Ib(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Cb(a,c),yb(a,c.id,c.bound,W,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Cb(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Cb(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",g=new FormData;a._formData.forEach(function(f,k){k.startsWith(e)&&g.append(k.slice(e.length),
f)});return g;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":vb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=W,a.then(zb(c,b,d),Ab(c)),null;default:throw a.reason;}}return c}
function Jb(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(g,f){return"string"===typeof f?Ib(e,this,g,f):f}};return e}function Kb(a){Bb(a,Error("Connection closed."))}function Lb(a,b,d){var c=pb(a,b);a=sb(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var g=U(c);return g.bind.apply(g,[null].concat(e))}):a?Promise.resolve(a).then(function(){return U(c)}):Promise.resolve(U(c))}
function Mb(a,b,d){a=Jb(b,d,a);Kb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=u({},a,!1);return new Proxy(a,ka)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,g){g.startsWith("$ACTION_")?g.startsWith("$ACTION_REF_")?(e="$ACTION_"+g.slice(12)+":",e=Mb(a,b,e),c=Lb(b,e.id,e.bound)):g.startsWith("$ACTION_ID_")&&(e=g.slice(11),c=Lb(b,e,null)):d.append(g,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};
exports.decodeFormState=function(a,b,d){var c=b.get("$ACTION_KEY");if("string"!==typeof c)return Promise.resolve(null);var e=null;b.forEach(function(f,k){k.startsWith("$ACTION_REF_")&&(f="$ACTION_"+k.slice(12)+":",e=Mb(b,d,f))});if(null===e)return Promise.resolve(null);var g=e.id;return Promise.resolve(e.bound).then(function(f){return null===f?null:[a,c,g,f.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=Jb(b,"",a);b=Z(a,0);Kb(a);return b};
exports.registerClientReference=function(a,b,d){return u(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:fa}})};
exports.renderToReadableStream=function(a,b,d){var c=db(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0);if(d&&d.signal){var e=d.signal;if(e.aborted)ob(c,e.reason);else{var g=function(){ob(c,e.reason);e.removeEventListener("abort",g)};e.addEventListener("abort",g)}}return new ReadableStream({type:"bytes",start:function(){c.flushScheduled=null!==c.destination;jb(c)},pull:function(f){if(1===c.status)c.status=2,ca(f,c.fatalError);else if(2!==c.status&&null===
c.destination){c.destination=f;try{O(c,f)}catch(k){M(c,k),nb(c,k)}}},cancel:function(f){c.destination=null;ob(c,f)}},{highWaterMark:0})};

//# sourceMappingURL=react-server-dom-webpack-server.browser.production.min.js.map
