{"version": 3, "sources": ["../../../src/client/components/bailout-to-client-rendering.ts"], "names": ["throwWithNoSSR", "staticGenerationAsyncStorage", "bailoutToClientRendering", "staticGenerationStore", "getStore", "forceStatic", "isStaticGeneration"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6CAA4C;AAC3E,SAASC,4BAA4B,QAAQ,6CAA4C;AAEzF,OAAO,SAASC;IACd,MAAMC,wBAAwBF,6BAA6BG,QAAQ;IAEnE,IAAID,yCAAAA,sBAAuBE,WAAW,EAAE;QACtC;IACF;IAEA,IAAIF,yCAAAA,sBAAuBG,kBAAkB,EAAE;QAC7CN;IACF;AACF"}