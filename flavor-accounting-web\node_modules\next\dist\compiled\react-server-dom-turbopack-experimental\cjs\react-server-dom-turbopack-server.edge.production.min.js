/*
 React
 react-server-dom-turbopack-server.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("react"),ba=require("react-dom"),m=null,n=0;function p(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<n&&(a.enqueue(new Uint8Array(m.buffer,0,n)),m=new Uint8Array(512),n=0),a.enqueue(b);else{var d=m.length-n;d<b.byteLength&&(0===d?a.enqueue(m):(m.set(b.subarray(0,d),n),a.enqueue(m),b=b.subarray(d)),m=new Uint8Array(512),n=0);m.set(b,n);n+=b.byteLength}return!0}var q=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),t=Symbol.for("react.server.reference");function u(a,b,d){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:d}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===t){var b=ea.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:fa}})}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}},ja={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];
case "__esModule":var d=a.$$id;a.default=u(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=u({},a.$$id,!0),e=new Proxy(c,ja);a.status="fulfilled";a.value=e;return a.then=u(function(f){return Promise.resolve(f(e))},
a.$$id+"#then",!1)}c=a[b];c||(c=u(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ia));return c},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");
}},ra={prefetchDNS:ka,preconnect:la,preload:ma,preloadModule:na,preinitStyle:oa,preinitScript:pa,preinitModuleScript:qa};function ka(a){if("string"===typeof a&&a){var b=v();if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),w(b,"D",a))}}}function la(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?w(d,"C",[a,b]):w(d,"C",a))}}}
function ma(a,b,d){if("string"===typeof a){var c=v();if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,k=d.imageSizes,h="";"string"===typeof g&&""!==g?(h+="["+g+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;f+="[image]"+h}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=x(d))?w(c,"L",[a,b,d]):w(c,"L",[a,b]))}}}function na(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"m",[a,b]):w(d,"m",a)}}}
function oa(a,b,d){if("string"===typeof a){var c=v();if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=x(d))?w(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?w(c,"S",[a,b]):w(c,"S",a)}}}function pa(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"X",[a,b]):w(d,"X",a)}}}function qa(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"M",[a,b]):w(d,"M",a)}}}
function x(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var sa=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,ta="function"===typeof AsyncLocalStorage,va=ta?new AsyncLocalStorage:null,y=Symbol.for("react.element"),wa=Symbol.for("react.fragment"),xa=Symbol.for("react.provider"),ya=Symbol.for("react.server_context"),za=Symbol.for("react.forward_ref"),Aa=Symbol.for("react.suspense"),Ba=Symbol.for("react.suspense_list"),Ca=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),B=Symbol.for("react.default_value"),Da=Symbol.for("react.memo_cache_sentinel"),
C=Symbol.for("react.postpone"),Ea=Symbol.iterator,D=null;function Fa(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Fa(a,d);b.context._currentValue=b.value}}}function Ga(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ga(a)}
function Ha(a){var b=a.parent;null!==b&&Ha(b);a.context._currentValue=a.value}function Ia(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Fa(a,b):Ia(a,b)}
function Ja(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?Fa(a,d):Ja(a,d);b.context._currentValue=b.value}function Ka(a){var b=D;b!==a&&(null===b?Ha(a):null===a?Ga(b):b.depth===a.depth?Fa(b,a):b.depth>a.depth?Ia(b,a):Ja(b,a),D=a)}function La(a,b){var d=a._currentValue;a._currentValue=b;var c=D;return D=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var Ma=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Na(){}function Oa(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Na,Na),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Pa=b;throw Ma;}}var Pa=null;
function Qa(){if(null===Pa)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Pa;Pa=null;return a}var E=null,Ra=0,F=null;function Sa(){var a=F;F=null;return a}function Ta(a){return a._currentValue}
var Xa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:H,useTransition:H,readContext:Ta,useContext:Ta,useReducer:H,useRef:H,useState:H,useInsertionEffect:H,useLayoutEffect:H,useImperativeHandle:H,useEffect:H,useId:Ua,useSyncExternalStore:H,useCacheRefresh:function(){return Va},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Da;return b},use:Wa};
function H(){throw Error("This Hook is not supported in Server Components.");}function Va(){throw Error("Refreshing the cache is not supported in Server Components.");}function Ua(){if(null===E)throw Error("useId can only be used while React is rendering");var a=E.identifierCount++;return":"+E.identifierPrefix+"S"+a.toString(32)+":"}
function Wa(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Ra;Ra+=1;null===F&&(F=[]);return Oa(F,a,b)}if(a.$$typeof===ya)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Ya(){return(new AbortController).signal}function Za(){var a=v();return a?a.cache:new Map}
var $a={getCacheSignal:function(){var a=Za(),b=a.get(Ya);void 0===b&&(b=Ya(),a.set(Ya,b));return b},getCacheForType:function(a){var b=Za(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},ab=Array.isArray,bb=Object.getPrototypeOf;function cb(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function db(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(ab(a))return"[...]";a=cb(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function eb(a){if("string"===typeof a)return a;switch(a){case Aa:return"Suspense";case Ba:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case za:return eb(a.render);case Ca:return eb(a.type);case z:var b=a._payload;a=a._init;try{return eb(a(b))}catch(d){}}return""}
function I(a,b){var d=cb(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(ab(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?I(g):db(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===y)e="<"+eb(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var k=f[g],h=JSON.stringify(k);e+=('"'+k+'"'===h?k:h)+": ";h=a[k];h="object"===typeof h&&null!==h?I(h):
db(h);k===b?(d=e.length,c=h.length,e+=h):e=10>h.length&&40>e.length+h.length?e+h:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var fb=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,gb=fb.ContextRegistry,J=aa.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!J)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var hb=Object.prototype,K=JSON.stringify,ib=J.TaintRegistryObjects,L=J.TaintRegistryValues,jb=J.TaintRegistryByteLengths,kb=J.TaintRegistryPendingRequests,lb=J.ReactCurrentCache,mb=fb.ReactCurrentDispatcher;function M(a){throw Error(a);}
function nb(a){a=a.taintCleanupQueue;kb.delete(a);for(var b=0;b<a.length;b++){var d=a[b],c=L.get(d);void 0!==c&&(1===c.count?L.delete(d):c.count--)}a.length=0}function ob(a){console.error(a)}function pb(){}
function qb(a,b,d,c,e,f){if(null!==lb.current&&lb.current!==$a)throw Error("Currently React only supports one RSC renderer at a time.");sa.current=ra;lb.current=$a;var g=new Set,k=[],h=[];kb.add(h);var l=new Set,A={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:l,abortableTasks:g,pingedTasks:k,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:h,onError:void 0===d?ob:d,onPostpone:void 0===f?pb:f,toJSON:function(ua,G){return rb(A,this,ua,G)}};A.pendingChunks++;b=sb(c);a=tb(A,a,b,g);k.push(a);return A}var N=null;function v(){if(N)return N;if(ta){var a=va.getStore();if(a)return a}return null}var ub={};
function vb(a,b){a.pendingChunks++;var d=tb(a,null,D,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,wb(a,d),d.id;case "rejected":var c=b.reason;"object"===typeof c&&null!==c&&c.$$typeof===C?(xb(a,c.message),yb(a,d.id)):(c=O(a,c),P(a,d.id,c));return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=
e;wb(a,d)},function(e){d.status=4;a.abortableTasks.delete(d);e=O(a,e);P(a,d.id,e);null!==a.destination&&Q(a,a.destination)});return d.id}function w(a,b,d){d=K(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;d=q.encode(b+d+"\n");a.completedHintChunks.push(d);zb(a)}function Ab(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function Bb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:z,_payload:a,_init:Ab}}
function R(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===r)return[y,b,d,e];Ra=0;F=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:Bb(e):e}if("string"===typeof b)return[y,b,d,e];if("symbol"===typeof b)return b===wa?e.children:[y,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===r)return[y,b,d,e];switch(b.$$typeof){case z:var g=
b._init;b=g(b._payload);return R(a,b,d,c,e,f);case za:return a=b.render,Ra=0,F=f,a(e,void 0);case Ca:return R(a,b.type,d,c,e,f);case xa:return La(b._context,e.value),[y,b,d,{value:e.value,children:e.children,__pop:ub}]}}throw Error("Unsupported Server Component type: "+db(b));}function wb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return Cb(a)},0))}
function tb(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return wb(a,e)},thenableState:null};c.add(e);return e}function S(a){return"$"+a.toString(16)}function Db(a,b,d){a=K(d);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function Eb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===y&&"1"===d?"$L"+g.toString(16):S(g);try{var k=a.bundlerConfig,h=c.$$id;g="";var l=k[h];if(l)g=l.name;else{var A=h.lastIndexOf("#");-1!==A&&(g=h.slice(A+1),l=k[h.slice(0,A)]);if(!l)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var ua=!0===c.$$async?[l.id,l.chunks,g,1]:[l.id,l.chunks,
g];a.pendingChunks++;var G=a.nextChunkId++,Wb=K(ua),Xb=G.toString(16)+":I"+Wb+"\n",Yb=q.encode(Xb);a.completedImportChunks.push(Yb);f.set(e,G);return b[0]===y&&"1"===d?"$L"+G.toString(16):S(G)}catch(Zb){return a.pendingChunks++,b=a.nextChunkId++,d=O(a,Zb),P(a,b,d),S(b)}}function T(a,b){a.pendingChunks++;b=tb(a,b,D,a.abortableTasks);Fb(a,b);return b.id}
function U(a,b,d){if(jb.has(d.byteLength)){var c=L.get(String.fromCharCode.apply(String,new Uint8Array(d.buffer,d.byteOffset,d.byteLength)));void 0!==c&&M(c.message)}a.pendingChunks+=2;c=a.nextChunkId++;var e=new Uint8Array(d.buffer,d.byteOffset,d.byteLength);d=512<d.byteLength?e.slice():e;e=d.byteLength;b=c.toString(16)+":"+b+e.toString(16)+",";b=q.encode(b);a.completedRegularChunks.push(b,d);return S(c)}var V=!1;
function rb(a,b,d,c){switch(c){case y:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===y||c.$$typeof===z);)try{switch(c.$$typeof){case y:var e=a.writtenObjects,f=e.get(c);if(void 0!==f){if(-1===f){var g=T(a,c);return S(g)}if(V===c)V=null;else return S(f)}else e.set(c,-1);var k=c;c=R(a,k.type,k.key,k.ref,k.props,null);break;case z:var h=c._init;c=h(c._payload)}}catch(l){d=l===Ma?Qa():l;if("object"===typeof d&&null!==d){if("function"===typeof d.then)return a.pendingChunks++,a=tb(a,c,D,a.abortableTasks),
c=a.ping,d.then(c,c),a.thenableState=Sa(),"$L"+a.id.toString(16);if(d.$$typeof===C)return c=d,a.pendingChunks++,d=a.nextChunkId++,xb(a,c.message),yb(a,d),"$L"+d.toString(16)}a.pendingChunks++;c=a.nextChunkId++;d=O(a,d);P(a,c,d);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){e=ib.get(c);void 0!==e&&M(e);if(c.$$typeof===r)return Eb(a,b,d,c);b=a.writtenObjects;e=b.get(c);if("function"===typeof c.then){if(void 0!==e)if(V===c)V=null;else return"$@"+e.toString(16);a=vb(a,c);b.set(c,
a);return"$@"+a.toString(16)}if(c.$$typeof===xa)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=Db(a,d,"$P"+c),a.completedRegularChunks.push(c)),S(d);if(c===ub){a=D;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===B?a.context._defaultValue:c;D=a.parent;return}if(void 0!==e){if(-1===e)return a=T(a,c),S(a);if(V===c)V=null;else return S(e)}else b.set(c,
-1);if(ab(c))return c;if(c instanceof Map){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d][0],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$Q"+T(a,c).toString(16)}if(c instanceof Set){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$W"+T(a,c).toString(16)}if(c instanceof ArrayBuffer)return U(a,"A",new Uint8Array(c));if(c instanceof Int8Array)return U(a,"C",c);if(c instanceof
Uint8Array)return U(a,"c",c);if(c instanceof Uint8ClampedArray)return U(a,"U",c);if(c instanceof Int16Array)return U(a,"S",c);if(c instanceof Uint16Array)return U(a,"s",c);if(c instanceof Int32Array)return U(a,"L",c);if(c instanceof Uint32Array)return U(a,"l",c);if(c instanceof Float32Array)return U(a,"F",c);if(c instanceof Float64Array)return U(a,"D",c);if(c instanceof BigInt64Array)return U(a,"N",c);if(c instanceof BigUint64Array)return U(a,"m",c);if(c instanceof DataView)return U(a,"V",c);null===
c||"object"!==typeof c?a=null:(a=Ea&&c[Ea]||c["@@iterator"],a="function"===typeof a?a:null);if(a)return Array.from(c);a=bb(c);if(a!==hb&&(null===a||null!==bb(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return c}if("string"===typeof c){e=L.get(c);void 0!==e&&M(e.message);if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,d=a.nextChunkId++,
c=q.encode(c),b=c.byteLength,b=d.toString(16)+":T"+b.toString(16)+",",b=q.encode(b),a.completedRegularChunks.push(b,c),S(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){e=ib.get(c);void 0!==e&&M(e);if(c.$$typeof===r)return Eb(a,b,d,c);if(c.$$typeof===t)return d=a.writtenServerReferences,
b=d.get(c),void 0!==b?a="$F"+b.toString(16):(b=c.$$bound,b={id:c.$$id,bound:b?Promise.resolve(b):null},a=T(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+I(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+I(b,d));}if("symbol"===typeof c){e=
a.writtenSymbols;f=e.get(c);if(void 0!==f)return S(f);f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+I(b,d));a.pendingChunks++;d=a.nextChunkId++;b=Db(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return S(d)}if("bigint"===typeof c)return a=L.get(c),void 0!==a&&M(a.message),"$n"+c.toString(10);throw Error("Type "+typeof c+
" is not supported in Client Component props."+I(b,d));}function xb(a,b){a=a.onPostpone;a(b)}function O(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}
function Gb(a,b){nb(a);null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}function yb(a,b){b=b.toString(16)+":P\n";b=q.encode(b);a.completedErrorChunks.push(b)}function P(a,b,d){d={digest:d};b=b.toString(16)+":E"+K(d)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}
function Fb(a,b){if(0===b.status){Ka(b.context);try{var d=b.model;if("object"===typeof d&&null!==d&&d.$$typeof===y){a.writtenObjects.set(d,b.id);var c=d,e=b.thenableState;b.model=d;d=R(a,c.type,c.key,c.ref,c.props,e);for(b.thenableState=null;"object"===typeof d&&null!==d&&d.$$typeof===y;)a.writtenObjects.set(d,b.id),c=d,b.model=d,d=R(a,c.type,c.key,c.ref,c.props,null)}"object"===typeof d&&null!==d&&a.writtenObjects.set(d,b.id);var f=b.id;V=d;var g=K(d,a.toJSON),k=f.toString(16)+":"+g+"\n",h=q.encode(k);
a.completedRegularChunks.push(h);a.abortableTasks.delete(b);b.status=1}catch(l){f=l===Ma?Qa():l;if("object"===typeof f&&null!==f){if("function"===typeof f.then){a=b.ping;f.then(a,a);b.thenableState=Sa();return}if(f.$$typeof===C){a.abortableTasks.delete(b);b.status=4;xb(a,f.message);yb(a,b.id);return}}a.abortableTasks.delete(b);b.status=4;f=O(a,f);P(a,b.id,f)}}}
function Cb(a){var b=mb.current;mb.current=Xa;var d=N;E=N=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++)Fb(a,c[e]);null!==a.destination&&Q(a,a.destination)}catch(f){O(a,f),Gb(a,f)}finally{mb.current=b,E=null,N=d}}
function Q(a,b){m=new Uint8Array(512);n=0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)a.pendingChunks--,p(b,d[c]);d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)p(b,e[c]);e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)a.pendingChunks--,p(b,f[c]);f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)a.pendingChunks--,p(b,g[c]);g.splice(0,c)}finally{a.flushScheduled=!1,m&&0<n&&(b.enqueue(new Uint8Array(m.buffer,0,n)),m=null,n=0)}0===a.pendingChunks&&
(nb(a),b.close())}function Hb(a){a.flushScheduled=null!==a.destination;ta?setTimeout(function(){return va.run(a,Cb,a)},0):setTimeout(function(){return Cb(a)},0)}function zb(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setTimeout(function(){return Q(a,b)},0)}}
function Ib(a,b){try{var d=a.abortableTasks;if(0<d.size){a.pendingChunks++;var c=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===C)xb(a,b.message),yb(a,c,b);else{var e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=O(a,e);P(a,c,f,e)}d.forEach(function(g){g.status=3;var k=S(c);g=Db(a,g.id,k);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&Q(a,a.destination)}catch(g){O(a,g),Gb(a,g)}}
function sb(a){if(a){var b=D;Ka(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];if(!gb[e]){var f={$$typeof:ya,_currentValue:B,_currentValue2:B,_defaultValue:B,_threadCount:0,Provider:null,Consumer:null,_globalName:e};f.Provider={$$typeof:xa,_context:f};gb[e]=f}La(gb[e],c)}a=D;Ka(b);return a}return null}
function Jb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[c.id,c.chunks,d]}var Kb=new Map;
function Lb(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function Mb(){}
function Nb(a){for(var b=a[1],d=[],c=0;c<b.length;c++){var e=b[c],f=Kb.get(e);if(void 0===f){f=globalThis.__next_chunk_load__(e);d.push(f);var g=Kb.set.bind(Kb,e,null);f.then(g,Mb);Kb.set(e,f)}else null!==f&&d.push(f)}return 4===a.length?0===d.length?Lb(a[0]):Promise.all(d).then(function(){return Lb(a[0])}):0<d.length?Promise.all(d):null}
function W(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function Ob(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}Ob.prototype=Object.create(Promise.prototype);
Ob.prototype.then=function(a,b){switch(this.status){case "resolved_model":Pb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Qb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function Rb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Qb(d,b)}}function Sb(a,b,d,c,e,f){var g=Jb(a._bundlerConfig,b);a=Nb(g);if(d)d=Promise.all([d,a]).then(function(k){k=k[0];var h=W(g);return h.bind.apply(h,[null].concat(k))});else if(a)d=Promise.resolve(a).then(function(){return W(g)});else return W(g);d.then(Tb(c,e,f),Ub(c));return null}var X=null,Y=null;
function Pb(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Vb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Rb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new Ob("resolved_model",c,null,a):new Ob("pending",null,null,a),d.set(b,c));return c}function Tb(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Qb(e,c.value))}}function Ub(a){return function(b){return Rb(a,b)}}
function $b(a,b){a=Z(a,b);"resolved_model"===a.status&&Pb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function ac(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=$b(a,c),Sb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=$b(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=$b(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,k){k.startsWith(e)&&f.append(k.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Pb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Tb(c,b,d),Ub(c)),null;default:throw a.reason;}}return c}
function bc(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?ac(e,this,f,g):g}};return e}function cc(a){Vb(a,Error("Connection closed."))}function dc(a,b,d){var c=Jb(a,b);a=Nb(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=W(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return W(c)}):Promise.resolve(W(c))}
function ec(a,b,d){a=bc(b,d,a);cc(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=u({},a,!1);return new Proxy(a,ja)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=ec(a,b,e),c=dc(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=dc(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=bc(b,"",a);b=Z(a,0);cc(a);return b};
exports.registerClientReference=function(a,b,d){return u(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:fa}})};
exports.renderToReadableStream=function(a,b,d){var c=qb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0);if(d&&d.signal){var e=d.signal;if(e.aborted)Ib(c,e.reason);else{var f=function(){Ib(c,e.reason);e.removeEventListener("abort",f)};e.addEventListener("abort",f)}}return new ReadableStream({type:"bytes",start:function(){Hb(c)},pull:function(g){if(1===c.status)c.status=2,ca(g,c.fatalError);else if(2!==c.status&&null===c.destination){c.destination=g;try{Q(c,
g)}catch(k){O(c,k),Gb(c,k)}}},cancel:function(){}},{highWaterMark:0})};

//# sourceMappingURL=react-server-dom-turbopack-server.edge.production.min.js.map
