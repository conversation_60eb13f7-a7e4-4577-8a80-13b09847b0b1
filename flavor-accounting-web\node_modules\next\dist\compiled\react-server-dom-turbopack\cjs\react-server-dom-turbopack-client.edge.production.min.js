/*
 React
 react-server-dom-turbopack-client.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var r=require("react-dom"),t=require("react"),u={stream:!0};function v(a,b){if(a){var c=a[b[0]];if(a=c[b[2]])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b[2]}return 4===b.length?[a.id,a.chunks,c,1]:[a.id,a.chunks,c]}return b}var w=new Map;
function x(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function aa(){}
function ba(a){for(var b=a[1],c=[],d=0;d<b.length;d++){var f=b[d],h=w.get(f);if(void 0===h){h=globalThis.__next_chunk_load__(f);c.push(h);var n=w.set.bind(w,f,null);h.then(n,aa);w.set(f,h)}else null!==h&&c.push(h)}return 4===a.length?0===c.length?x(a[0]):Promise.all(c).then(function(){return x(a[0])}):0<c.length?Promise.all(c):null}
function ca(a,b,c){if(null!==a)for(var d=0;d<b.length;d++){var f=c,h=y.current;if(h){var n=h.preinitScript,k=a.prefix+b[d];var m=a.crossOrigin;m="string"===typeof m?"use-credentials"===m?m:"":void 0;n.call(h,k,{crossOrigin:m,nonce:f})}}}var y=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,z=Symbol.for("react.element"),da=Symbol.for("react.provider"),ea=Symbol.for("react.server_context"),fa=Symbol.for("react.lazy"),A=Symbol.for("react.default_value"),B=Symbol.iterator;
function ha(a){if(null===a||"object"!==typeof a)return null;a=B&&a[B]||a["@@iterator"];return"function"===typeof a?a:null}var ia=Array.isArray,C=Object.getPrototypeOf,ja=Object.prototype,D=new WeakMap;function ka(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function F(a,b,c,d){function f(m,e){if(null===e)return null;if("object"===typeof e){if("function"===typeof e.then){null===k&&(k=new FormData);n++;var l=h++;e.then(function(p){p=JSON.stringify(p,f);var q=k;q.append(b+l,p);n--;0===n&&c(q)},function(p){d(p)});return"$@"+l.toString(16)}if(ia(e))return e;if(e instanceof FormData){null===k&&(k=new FormData);var g=k;m=h++;var E=b+m+"_";e.forEach(function(p,q){g.append(E+q,p)});return"$K"+m.toString(16)}if(e instanceof Map)return e=JSON.stringify(Array.from(e),
f),null===k&&(k=new FormData),m=h++,k.append(b+m,e),"$Q"+m.toString(16);if(e instanceof Set)return e=JSON.stringify(Array.from(e),f),null===k&&(k=new FormData),m=h++,k.append(b+m,e),"$W"+m.toString(16);if(ha(e))return Array.from(e);m=C(e);if(m!==ja&&(null===m||null!==C(m)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return e}if("string"===typeof e){if("Z"===e[e.length-1]&&this[m]instanceof Date)return"$D"+e;
e="$"===e[0]?"$"+e:e;return e}if("boolean"===typeof e)return e;if("number"===typeof e)return ka(e);if("undefined"===typeof e)return"$undefined";if("function"===typeof e){e=D.get(e);if(void 0!==e)return e=JSON.stringify(e,f),null===k&&(k=new FormData),m=h++,k.set(b+m,e),"$F"+m.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof e){m=e.description;if(Symbol.for(m)!==e)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+
(e.description+") cannot be found among global symbols."));return"$S"+m}if("bigint"===typeof e)return"$n"+e.toString(10);throw Error("Type "+typeof e+" is not supported as an argument to a Server Function.");}var h=1,n=0,k=null;a=JSON.stringify(a,f);null===k?c(a):(k.set(b+"0",a),0===n&&c(k))}var G=new WeakMap;
function la(a){var b,c,d=new Promise(function(f,h){b=f;c=h});F(a,"",function(f){if("string"===typeof f){var h=new FormData;h.append("0",f);f=h}d.status="fulfilled";d.value=f;b(f)},function(f){d.status="rejected";d.reason=f;c(f)});return d}
function ma(a){var b=D.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var c=null;if(null!==b.bound){c=G.get(b);c||(c=la(b),G.set(b,c));if("rejected"===c.status)throw c.reason;if("fulfilled"!==c.status)throw c;b=c.value;var d=new FormData;b.forEach(function(f,h){d.append("$ACTION_"+a+":"+h,f)});c=d;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:c}}
function na(a,b){var c=D.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(c.id!==a)return!1;var d=c.bound;if(null===d)return 0===b;switch(d.status){case "fulfilled":return d.value.length===b;case "pending":throw d;case "rejected":throw d.reason;default:throw"string"!==typeof d.status&&(d.status="pending",d.then(function(f){d.status="fulfilled";d.value=f},function(f){d.status="rejected";d.reason=f})),d;}}
function H(a,b){Object.defineProperties(a,{$$FORM_ACTION:{value:ma},$$IS_SIGNATURE_EQUAL:{value:na},bind:{value:oa}});D.set(a,b)}var pa=Function.prototype.bind,qa=Array.prototype.slice;function oa(){var a=pa.apply(this,arguments),b=D.get(this);if(b){var c=qa.call(arguments,1),d=null;d=null!==b.bound?Promise.resolve(b.bound).then(function(f){return f.concat(c)}):Promise.resolve(c);H(a,{id:b.id,bound:d})}return a}
function ra(a,b){function c(){var d=Array.prototype.slice.call(arguments);return b(a,d)}H(c,{id:a,bound:null});return c}var I=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function J(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}J.prototype=Object.create(Promise.prototype);
J.prototype.then=function(a,b){switch(this.status){case "resolved_model":L(this);break;case "resolved_module":M(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":case "cyclic":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function sa(a){switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":throw a;default:throw a.reason;}}function N(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function O(a,b,c){switch(a.status){case "fulfilled":N(b,a.value);break;case "pending":case "blocked":case "cyclic":a.value=b;a.reason=c;break;case "rejected":c&&N(c,a.reason)}}
function P(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&N(c,b)}}function Q(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(M(a),O(a,c,d))}}var R=null,S=null;
function L(a){var b=R,c=S;R=a;S=null;var d=a.value;a.status="cyclic";a.value=null;a.reason=null;try{var f=JSON.parse(d,a._response._fromJSON);if(null!==S&&0<S.deps)S.value=f,a.status="blocked",a.value=null,a.reason=null;else{var h=a.value;a.status="fulfilled";a.value=f;null!==h&&N(h,f)}}catch(n){a.status="rejected",a.reason=n}finally{R=b,S=c}}
function M(a){try{var b=a.value,c=globalThis.__next_require__(b[0]);if(4===b.length&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b[2]?c:""===b[2]?c.__esModule?c.default:c:c[b[2]];a.status="fulfilled";a.value=d}catch(f){a.status="rejected",a.reason=f}}function T(a,b){a._chunks.forEach(function(c){"pending"===c.status&&P(c,b)})}function U(a,b){var c=a._chunks,d=c.get(b);d||(d=new J("pending",null,null,a),c.set(b,d));return d}
function ta(a,b,c,d){if(S){var f=S;d||f.deps++}else f=S={deps:d?0:1,value:null};return function(h){b[c]=h;f.deps--;0===f.deps&&"blocked"===a.status&&(h=a.value,a.status="fulfilled",a.value=f.value,null!==h&&N(h,f.value))}}function ua(a){return function(b){return P(a,b)}}
function va(a,b){function c(){var f=Array.prototype.slice.call(arguments),h=b.bound;return h?"fulfilled"===h.status?d(b.id,h.value.concat(f)):Promise.resolve(h).then(function(n){return d(b.id,n.concat(f))}):d(b.id,f)}var d=a._callServer;H(c,b);return c}function W(a,b){a=U(a,b);switch(a.status){case "resolved_model":L(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function wa(a,b,c,d){if("$"===d[0]){if("$"===d)return z;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=U(a,b),{$$typeof:fa,_payload:a,_init:sa};case "@":return b=parseInt(d.slice(2),16),U(a,b);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),I[a]||(b={$$typeof:ea,_currentValue:A,_currentValue2:A,_defaultValue:A,_threadCount:0,Provider:null,Consumer:null,_globalName:a},b.Provider={$$typeof:da,_context:b},I[a]=b),I[a].Provider;case "F":return b=
parseInt(d.slice(2),16),b=W(a,b),va(a,b);case "Q":return b=parseInt(d.slice(2),16),a=W(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=W(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=U(a,d);switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;
case "pending":case "blocked":case "cyclic":return d=R,a.then(ta(d,b,c,"cyclic"===a.status),ua(d)),null;default:throw a.reason;}}}return d}function xa(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}
function ya(a,b,c){var d=a._chunks,f=d.get(b);c=JSON.parse(c,a._fromJSON);var h=v(a._bundlerConfig,c);ca(a._moduleLoading,c[1],a._nonce);if(c=ba(h)){if(f){var n=f;n.status="blocked"}else n=new J("blocked",null,null,a),d.set(b,n);c.then(function(){return Q(n,h)},function(k){return P(n,k)})}else f?Q(f,h):d.set(b,new J("resolved_module",h,null,a))}
function za(a){return function(b,c){return"string"===typeof c?wa(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===z?{$$typeof:z,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}function X(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
function Y(a){var b=a.ssrManifest.moduleMap,c=a.ssrManifest.moduleLoading;a="string"===typeof a.nonce?a.nonce:void 0;var d=new Map;b={_bundlerConfig:b,_moduleLoading:c,_callServer:void 0!==X?X:xa,_nonce:a,_chunks:d,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};b._fromJSON=za(b);return b}
function Z(a,b){function c(h){var n=h.value;if(h.done)T(a,Error("Connection closed."));else{var k=0,m=a._rowState,e=a._rowID,l=a._rowTag,g=a._rowLength;h=a._buffer;for(var E=n.length;k<E;){var p=-1;switch(m){case 0:p=n[k++];58===p?m=1:e=e<<4|(96<p?p-87:p-48);continue;case 1:m=n[k];84===m?(l=m,m=2,k++):64<m&&91>m?(l=m,m=3,k++):(l=0,m=3);continue;case 2:p=n[k++];44===p?m=4:g=g<<4|(96<p?p-87:p-48);continue;case 3:p=n.indexOf(10,k);break;case 4:p=k+g,p>n.length&&(p=-1)}var q=n.byteOffset+k;if(-1<p){k=
new Uint8Array(n.buffer,q,p-k);g=a;q=l;var V=g._stringDecoder;l="";for(var K=0;K<h.length;K++)l+=V.decode(h[K],u);l+=V.decode(k);switch(q){case 73:ya(g,e,l);break;case 72:e=l[0];l=l.slice(1);g=JSON.parse(l,g._fromJSON);if(l=y.current)switch(e){case "D":l.prefetchDNS(g);break;case "C":"string"===typeof g?l.preconnect(g):l.preconnect(g[0],g[1]);break;case "L":e=g[0];k=g[1];3===g.length?l.preload(e,k,g[2]):l.preload(e,k);break;case "m":"string"===typeof g?l.preloadModule(g):l.preloadModule(g[0],g[1]);
break;case "S":"string"===typeof g?l.preinitStyle(g):l.preinitStyle(g[0],0===g[1]?void 0:g[1],3===g.length?g[2]:void 0);break;case "X":"string"===typeof g?l.preinitScript(g):l.preinitScript(g[0],g[1]);break;case "M":"string"===typeof g?l.preinitModuleScript(g):l.preinitModuleScript(g[0],g[1])}break;case 69:l=JSON.parse(l);k=l.digest;l=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
l.stack="Error: "+l.message;l.digest=k;k=g._chunks;(q=k.get(e))?P(q,l):k.set(e,new J("rejected",null,l,g));break;case 84:g._chunks.set(e,new J("fulfilled",l,null,g));break;default:k=g._chunks,(q=k.get(e))?(g=q,e=l,"pending"===g.status&&(l=g.value,k=g.reason,g.status="resolved_model",g.value=e,null!==l&&(L(g),O(g,l,k)))):k.set(e,new J("resolved_model",l,null,g))}k=p;3===m&&k++;g=e=l=m=0;h.length=0}else{n=new Uint8Array(n.buffer,q,n.byteLength-k);h.push(n);g-=n.byteLength;break}}a._rowState=m;a._rowID=
e;a._rowTag=l;a._rowLength=g;return f.read().then(c).catch(d)}}function d(h){T(a,h)}var f=b.getReader();f.read().then(c).catch(d)}exports.createFromFetch=function(a,b){var c=Y(b);a.then(function(d){Z(c,d.body)},function(d){T(c,d)});return U(c,0)};exports.createFromReadableStream=function(a,b){b=Y(b);Z(b,a);return U(b,0)};exports.createServerReference=function(a){return ra(a,X)};exports.encodeReply=function(a){return new Promise(function(b,c){F(a,"",b,c)})};

//# sourceMappingURL=react-server-dom-turbopack-client.edge.production.min.js.map
