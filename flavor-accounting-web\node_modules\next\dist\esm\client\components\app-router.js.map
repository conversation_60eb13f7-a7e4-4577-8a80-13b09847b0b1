{"version": 3, "sources": ["../../../src/client/components/app-router.tsx"], "names": ["React", "use", "useEffect", "useMemo", "useCallback", "startTransition", "useInsertionEffect", "AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "CacheStates", "ACTION_FAST_REFRESH", "ACTION_NAVIGATE", "ACTION_PREFETCH", "ACTION_REFRESH", "ACTION_RESTORE", "ACTION_SERVER_ACTION", "ACTION_SERVER_PATCH", "PrefetchKind", "createHrefFromUrl", "SearchParamsContext", "PathnameContext", "useReducerWithReduxDevtools", "useUnwrapState", "Error<PERSON>ou<PERSON><PERSON>", "createInitialRouterState", "isBot", "addBasePath", "AppRouterAnnouncer", "RedirectBoundary", "findHeadInCache", "createInfinitePromise", "NEXT_RSC_UNION_QUERY", "removeBasePath", "has<PERSON>ase<PERSON><PERSON>", "isServer", "window", "initialParallelRoutes", "Map", "globalServerActionDispatcher", "getServerActionDispatcher", "globalMutable", "urlToUrlWithoutFlightMarker", "url", "urlWithoutFlightParameters", "URL", "location", "origin", "searchParams", "delete", "process", "env", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "length", "slice", "isExternalURL", "HistoryUpdater", "appRouterState", "sync", "tree", "pushRef", "canonicalUrl", "historyState", "__NEXT_WINDOW_HISTORY_SUPPORT", "preserveCustomHistoryState", "history", "state", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pendingPush", "href", "pushState", "replaceState", "createEmptyCacheNode", "status", "LAZY_INITIALIZED", "data", "subTreeData", "parallelRoutes", "useServerActionDispatcher", "dispatch", "serverActionDispatcher", "actionPayload", "type", "useChangeByServerResponse", "previousTree", "flightData", "overrideCanonicalUrl", "useNavigate", "navigateType", "shouldScroll", "isExternalUrl", "locationSearch", "search", "copyNextJsInternalHistoryState", "currentState", "Router", "buildId", "initialHead", "initialTree", "initialCanonicalUrl", "initialSeedData", "assetPrefix", "initialState", "reducerState", "changeByServerResponse", "navigate", "appRouter", "routerInstance", "back", "forward", "prefetch", "options", "navigator", "userAgent", "kind", "FULL", "replace", "scroll", "push", "refresh", "fastRefresh", "Error", "next", "router", "cache", "prefetchCache", "nd", "handlePageShow", "event", "persisted", "addEventListener", "removeEventListener", "mpaNavigation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assign", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "nextUrl", "focusAndScrollRef", "head", "content", "DevRootNotFoundBoundary", "require", "HotReloader", "default", "Provider", "value", "childNodes", "AppRouter", "props", "globalErrorComponent", "rest", "errorComponent"], "mappings": "AAAA;AAGA,OAAOA,SACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,WAAW,EACXC,eAAe,EACfC,kBAAkB,QACb,QAAO;AACd,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,yBAAyB,EACzBC,WAAW,QACN,qDAAoD;AAU3D,SACEC,mBAAmB,EACnBC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,oBAAoB,EACpBC,mBAAmB,EACnBC,YAAY,QACP,wCAAuC;AAQ9C,SAASC,iBAAiB,QAAQ,wCAAuC;AACzE,SACEC,mBAAmB,EACnBC,eAAe,QACV,uDAAsD;AAC7D,SACEC,2BAA2B,EAC3BC,cAAc,QAET,8BAA6B;AACpC,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,wBAAwB,QAAQ,+CAA8C;AAEvF,SAASC,KAAK,QAAQ,uCAAsC;AAC5D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,eAAe,QAAQ,+CAA8C;AAC9E,SAASC,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,oBAAoB,QAAQ,uBAAsB;AAC3D,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,MAAMC,WAAW,OAAOC,WAAW;AAEnC,iHAAiH;AACjH,IAAIC,wBAAqDF,WACrD,OACA,IAAIG;AAER,IAAIC,+BAA+B;AAEnC,OAAO,SAASC;IACd,OAAOD;AACT;AAEA,MAAME,gBAEF,CAAC;AAEL,OAAO,SAASC,4BAA4BC,GAAW;IACrD,MAAMC,6BAA6B,IAAIC,IAAIF,KAAKG,SAASC,MAAM;IAC/DH,2BAA2BI,YAAY,CAACC,MAAM,CAACjB;IAC/C,IAAIkB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IACEF,QAAQC,GAAG,CAACE,oBAAoB,KAAK,YACrCT,2BAA2BU,QAAQ,CAACC,QAAQ,CAAC,SAC7C;YACA,MAAM,EAAED,QAAQ,EAAE,GAAGV;YACrB,MAAMY,SAASF,SAASC,QAAQ,CAAC,gBAAgB,KAAK;YACtD,gEAAgE;YAChEX,2BAA2BU,QAAQ,GAAGA,SAASG,KAAK,CAAC,GAAG,CAACD;QAC3D;IACF;IACA,OAAOZ;AACT;AAWA,SAASc,cAAcf,GAAQ;IAC7B,OAAOA,IAAII,MAAM,KAAKX,OAAOU,QAAQ,CAACC,MAAM;AAC9C;AAEA,SAASY,eAAe,KAMvB;IANuB,IAAA,EACtBC,cAAc,EACdC,IAAI,EAIL,GANuB;IAOtBvD,mBAAmB;QACjB,MAAM,EAAEwD,IAAI,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGJ;QACxC,MAAMK,eAAe;YACnB,GAAIf,QAAQC,GAAG,CAACe,6BAA6B,IAC7CH,QAAQI,0BAA0B,GAC9B/B,OAAOgC,OAAO,CAACC,KAAK,GACpB,CAAC,CAAC;YACN,yCAAyC;YACzC,kFAAkF;YAClF,iFAAiF;YACjFC,MAAM;YACNC,iCAAiCT;QACnC;QACA,IACEC,QAAQS,WAAW,IACnB,+FAA+F;QAC/F,2DAA2D;QAC3DrD,kBAAkB,IAAI0B,IAAIT,OAAOU,QAAQ,CAAC2B,IAAI,OAAOT,cACrD;YACA,qJAAqJ;YACrJD,QAAQS,WAAW,GAAG;YACtBpC,OAAOgC,OAAO,CAACM,SAAS,CAACT,cAAc,IAAID;QAC7C,OAAO;YACL5B,OAAOgC,OAAO,CAACO,YAAY,CAACV,cAAc,IAAID;QAChD;QAEAH,KAAKD;IACP,GAAG;QAACA;QAAgBC;KAAK;IACzB,OAAO;AACT;AAEA,OAAO,MAAMe,uBAAuB,IAAO,CAAA;QACzCC,QAAQnE,YAAYoE,gBAAgB;QACpCC,MAAM;QACNC,aAAa;QACbC,gBAAgB,IAAI3C;IACtB,CAAA,EAAE;AAEF,SAAS4C,0BAA0BC,QAAwC;IACzE,MAAMC,yBAAiDhF,YACrD,CAACiF;QACChF,gBAAgB;YACd8E,SAAS;gBACP,GAAGE,aAAa;gBAChBC,MAAMtE;YACR;QACF;IACF,GACA;QAACmE;KAAS;IAEZ5C,+BAA+B6C;AACjC;AAEA;;CAEC,GACD,SAASG,0BACPJ,QAAwC;IAExC,OAAO/E,YACL,CACEoF,cACAC,YACAC;QAEArF,gBAAgB;YACd8E,SAAS;gBACPG,MAAMrE;gBACNwE;gBACAD;gBACAE;YACF;QACF;IACF,GACA;QAACP;KAAS;AAEd;AAEA,SAASQ,YAAYR,QAAwC;IAC3D,OAAO/E,YACL,CAACqE,MAAMmB,cAAcC;QACnB,MAAMlD,MAAM,IAAIE,IAAIlB,YAAY8C,OAAO3B,SAAS2B,IAAI;QAEpD,OAAOU,SAAS;YACdG,MAAM1E;YACN+B;YACAmD,eAAepC,cAAcf;YAC7BoD,gBAAgBjD,SAASkD,MAAM;YAC/BH,cAAcA,uBAAAA,eAAgB;YAC9BD;QACF;IACF,GACA;QAACT;KAAS;AAEd;AAEA,SAASc,+BAA+BlB,IAAS;IAC/C,IAAIA,QAAQ,MAAMA,OAAO,CAAC;IAC1B,MAAMmB,eAAe9D,OAAOgC,OAAO,CAACC,KAAK;IACzC,MAAMC,OAAO4B,gCAAAA,aAAc5B,IAAI;IAC/B,IAAIA,MAAM;QACRS,KAAKT,IAAI,GAAGA;IACd;IACA,MAAMC,kCACJ2B,gCAAAA,aAAc3B,+BAA+B;IAC/C,IAAIA,iCAAiC;QACnCQ,KAAKR,+BAA+B,GAAGA;IACzC;IAEA,OAAOQ;AACT;AAEA;;CAEC,GACD,SAASoB,OAAO,KAOC;IAPD,IAAA,EACdC,OAAO,EACPC,WAAW,EACXC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfC,WAAW,EACI,GAPD;IAQd,MAAMC,eAAevG,QACnB,IACEsB,yBAAyB;YACvB2E;YACAI;YACAD;YACAD;YACAjE;YACAF;YACAW,UAAU,CAACX,WAAWC,OAAOU,QAAQ,GAAG;YACxCuD;QACF,IACF;QAACD;QAASI;QAAiBD;QAAqBD;QAAaD;KAAY;IAE3E,MAAM,CAACM,cAAcxB,UAAUtB,KAAK,GAClCvC,4BAA4BoF;IAE9BxG,UAAU;QACR,yEAAyE;QACzEmC,wBAAwB;IAC1B,GAAG,EAAE;IAEL,MAAM,EAAE2B,YAAY,EAAE,GAAGzC,eAAeoF;IACxC,mEAAmE;IACnE,MAAM,EAAE3D,YAAY,EAAEM,QAAQ,EAAE,GAAGnD,QAAQ;QACzC,MAAMwC,MAAM,IAAIE,IACdmB,cACA,OAAO5B,WAAW,cAAc,aAAaA,OAAOU,QAAQ,CAAC2B,IAAI;QAGnE,OAAO;YACL,4DAA4D;YAC5DzB,cAAcL,IAAIK,YAAY;YAC9BM,UAAUpB,YAAYS,IAAIW,QAAQ,IAC9BrB,eAAeU,IAAIW,QAAQ,IAC3BX,IAAIW,QAAQ;QAClB;IACF,GAAG;QAACU;KAAa;IAEjB,MAAM4C,yBAAyBrB,0BAA0BJ;IACzD,MAAM0B,WAAWlB,YAAYR;IAC7BD,0BAA0BC;IAE1B;;GAEC,GACD,MAAM2B,YAAY3G,QAA2B;QAC3C,MAAM4G,iBAAoC;YACxCC,MAAM,IAAM5E,OAAOgC,OAAO,CAAC4C,IAAI;YAC/BC,SAAS,IAAM7E,OAAOgC,OAAO,CAAC6C,OAAO;YACrCC,UAAU,CAACzC,MAAM0C;gBACf,kDAAkD;gBAClD,uEAAuE;gBACvE,IACEzF,MAAMU,OAAOgF,SAAS,CAACC,SAAS,KAChCnE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzB;oBACA;gBACF;gBACA,MAAMT,MAAM,IAAIE,IAAIlB,YAAY8C,OAAOrC,OAAOU,QAAQ,CAAC2B,IAAI;gBAC3D,qDAAqD;gBACrD,IAAIf,cAAcf,MAAM;oBACtB;gBACF;gBACAtC,gBAAgB;wBAIN8G;oBAHRhC,SAAS;wBACPG,MAAMzE;wBACN8B;wBACA2E,MAAMH,CAAAA,gBAAAA,2BAAAA,QAASG,IAAI,YAAbH,gBAAiBjG,aAAaqG,IAAI;oBAC1C;gBACF;YACF;YACAC,SAAS,CAAC/C,MAAM0C;oBAAAA,oBAAAA,UAAU,CAAC;gBACzB9G,gBAAgB;wBACY8G;oBAA1BN,SAASpC,MAAM,WAAW0C,CAAAA,kBAAAA,QAAQM,MAAM,YAAdN,kBAAkB;gBAC9C;YACF;YACAO,MAAM,CAACjD,MAAM0C;oBAAAA,oBAAAA,UAAU,CAAC;gBACtB9G,gBAAgB;wBACS8G;oBAAvBN,SAASpC,MAAM,QAAQ0C,CAAAA,kBAAAA,QAAQM,MAAM,YAAdN,kBAAkB;gBAC3C;YACF;YACAQ,SAAS;gBACPtH,gBAAgB;oBACd8E,SAAS;wBACPG,MAAMxE;wBACNiC,QAAQX,OAAOU,QAAQ,CAACC,MAAM;oBAChC;gBACF;YACF;YACA,wDAAwD;YACxD6E,aAAa;gBACX,IAAI1E,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;oBAC1C,MAAM,IAAIyE,MACR;gBAEJ,OAAO;oBACLxH,gBAAgB;wBACd8E,SAAS;4BACPG,MAAM3E;4BACNoC,QAAQX,OAAOU,QAAQ,CAACC,MAAM;wBAChC;oBACF;gBACF;YACF;QACF;QAEA,OAAOgE;IACT,GAAG;QAAC5B;QAAU0B;KAAS;IAEvB3G,UAAU;QACR,gEAAgE;QAChE,IAAIkC,OAAO0F,IAAI,EAAE;YACf1F,OAAO0F,IAAI,CAACC,MAAM,GAAGjB;QACvB;IACF,GAAG;QAACA;KAAU;IAEd,IAAI5D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,sDAAsD;QACtD,MAAM,EAAE4E,KAAK,EAAEC,aAAa,EAAEnE,IAAI,EAAE,GAAGvC,eAAeoF;QAEtD,4FAA4F;QAC5F,sDAAsD;QACtDzG,UAAU;YACR,0CAA0C;YAC1C,uGAAuG;YACvG,mCAAmC;YACnCkC,OAAO8F,EAAE,GAAG;gBACVH,QAAQjB;gBACRkB;gBACAC;gBACAnE;YACF;QACF,GAAG;YAACgD;YAAWkB;YAAOC;YAAenE;SAAK;IAC5C;IAEA5D,UAAU;QACR,0DAA0D;QAC1D,uFAAuF;QACvF,qEAAqE;QACrE,wGAAwG;QACxG,SAASiI,eAAeC,KAA0B;gBAG7ChG;YAFH,IACE,CAACgG,MAAMC,SAAS,IAChB,GAACjG,wBAAAA,OAAOgC,OAAO,CAACC,KAAK,qBAApBjC,sBAAsBmC,+BAA+B,GACtD;gBACA;YACF;YAEAY,SAAS;gBACPG,MAAMvE;gBACN4B,KAAK,IAAIE,IAAIT,OAAOU,QAAQ,CAAC2B,IAAI;gBACjCX,MAAM1B,OAAOgC,OAAO,CAACC,KAAK,CAACE,+BAA+B;YAC5D;QACF;QAEAnC,OAAOkG,gBAAgB,CAAC,YAAYH;QAEpC,OAAO;YACL/F,OAAOmG,mBAAmB,CAAC,YAAYJ;QACzC;IACF,GAAG;QAAChD;KAAS;IAEb,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,YAAY;IACZ,EAAE;IACF,sEAAsE;IACtE,6EAA6E;IAC7E,6EAA6E;IAC7E,uBAAuB;IACvB,MAAM,EAAEpB,OAAO,EAAE,GAAGxC,eAAeoF;IACnC,IAAI5C,QAAQyE,aAAa,EAAE;QACzB,gHAAgH;QAChH,IAAI/F,cAAcgG,cAAc,KAAKzE,cAAc;YACjD,MAAMlB,YAAWV,OAAOU,QAAQ;YAChC,IAAIiB,QAAQS,WAAW,EAAE;gBACvB1B,UAAS4F,MAAM,CAAC1E;YAClB,OAAO;gBACLlB,UAAS0E,OAAO,CAACxD;YACnB;YAEAvB,cAAcgG,cAAc,GAAGzE;QACjC;QACA,mEAAmE;QACnE,4EAA4E;QAC5E,+BAA+B;QAC/B/D,IAAI8B;IACN;IAEA7B,UAAU;QACR,MAAMyI,oBAAoBvG,OAAOgC,OAAO,CAACM,SAAS,CAACkE,IAAI,CAACxG,OAAOgC,OAAO;QACtE,MAAMyE,uBAAuBzG,OAAOgC,OAAO,CAACO,YAAY,CAACiE,IAAI,CAC3DxG,OAAOgC,OAAO;QAEhB,IAAIlB,QAAQC,GAAG,CAACe,6BAA6B,EAAE;YAC7C,wJAAwJ;YACxJ,MAAM4E,iCAAiC,CACrCnG;gBAEA,MAAM8B,OAAOrC,OAAOU,QAAQ,CAAC2B,IAAI;gBACjCpE,gBAAgB;oBACd8E,SAAS;wBACPG,MAAMvE;wBACN4B,KAAK,IAAIE,IAAIF,cAAAA,MAAO8B,MAAMA;wBAC1BX,MAAM1B,OAAOgC,OAAO,CAACC,KAAK,CAACE,+BAA+B;oBAC5D;gBACF;YACF;YAEA;;;;OAIC,GACDnC,OAAOgC,OAAO,CAACM,SAAS,GAAG,SAASA,UAClCK,IAAS,EACTgE,OAAe,EACfpG,GAAyB;gBAEzB,qEAAqE;gBACrE,IAAIoC,CAAAA,wBAAAA,KAAMT,IAAI,MAAIS,wBAAAA,KAAMiE,EAAE,GAAE;oBAC1B,OAAOL,kBAAkB5D,MAAMgE,SAASpG;gBAC1C;gBACAoC,OAAOkB,+BAA+BlB;gBAEtC,IAAIpC,KAAK;oBACPmG,+BAA+BnG;gBACjC;gBAEA,OAAOgG,kBAAkB5D,MAAMgE,SAASpG;YAC1C;YAEA;;;;OAIC,GACDP,OAAOgC,OAAO,CAACO,YAAY,GAAG,SAASA,aACrCI,IAAS,EACTgE,OAAe,EACfpG,GAAyB;gBAEzB,qEAAqE;gBACrE,IAAIoC,CAAAA,wBAAAA,KAAMT,IAAI,MAAIS,wBAAAA,KAAMiE,EAAE,GAAE;oBAC1B,OAAOH,qBAAqB9D,MAAMgE,SAASpG;gBAC7C;gBACAoC,OAAOkB,+BAA+BlB;gBAEtC,IAAIpC,KAAK;oBACPmG,+BAA+BnG;gBACjC;gBACA,OAAOkG,qBAAqB9D,MAAMgE,SAASpG;YAC7C;QACF;QAEA;;;;KAIC,GACD,MAAMsG,aAAa;gBAAC,EAAE5E,KAAK,EAAiB;YAC1C,IAAI,CAACA,OAAO;gBACV,+IAA+I;gBAC/I;YACF;YAEA,6EAA6E;YAC7E,IAAI,CAACA,MAAMC,IAAI,EAAE;gBACflC,OAAOU,QAAQ,CAACoG,MAAM;gBACtB;YACF;YAEA,kCAAkC;YAClC,gHAAgH;YAChH,oEAAoE;YACpE7I,gBAAgB;gBACd8E,SAAS;oBACPG,MAAMvE;oBACN4B,KAAK,IAAIE,IAAIT,OAAOU,QAAQ,CAAC2B,IAAI;oBACjCX,MAAMO,MAAME,+BAA+B;gBAC7C;YACF;QACF;QAEA,8CAA8C;QAC9CnC,OAAOkG,gBAAgB,CAAC,YAAYW;QACpC,OAAO;YACL,IAAI/F,QAAQC,GAAG,CAACe,6BAA6B,EAAE;gBAC7C9B,OAAOgC,OAAO,CAACM,SAAS,GAAGiE;gBAC3BvG,OAAOgC,OAAO,CAACO,YAAY,GAAGkE;YAChC;YACAzG,OAAOmG,mBAAmB,CAAC,YAAYU;QACzC;IACF,GAAG;QAAC9D;KAAS;IAEb,MAAM,EAAE6C,KAAK,EAAElE,IAAI,EAAEqF,OAAO,EAAEC,iBAAiB,EAAE,GAC/C7H,eAAeoF;IAEjB,MAAM0C,OAAOlJ,QAAQ;QACnB,OAAO2B,gBAAgBkG,OAAOlE,IAAI,CAAC,EAAE;IACvC,GAAG;QAACkE;QAAOlE;KAAK;IAEhB,IAAIwF,wBACF,oBAACzH,wBACEwH,MACArB,MAAMhD,WAAW,gBAClB,oBAACpD;QAAmBkC,MAAMA;;IAI9B,IAAIZ,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAOhB,WAAW,aAAa;YACjC,MAAMmH,0BACJC,QAAQ,iCAAiCD,uBAAuB;YAClED,wBAAU,oBAACC,+BAAyBD;QACtC;QACA,MAAMG,cACJD,QAAQ,2CAA2CE,OAAO;QAE5DJ,wBAAU,oBAACG;YAAYhD,aAAaA;WAAc6C;IACpD;IAEA,qBACE,wDACE,oBAAC3F;QACCC,gBAAgBrC,eAAeoF;QAC/B9C,MAAMA;sBAER,oBAACxC,gBAAgBsI,QAAQ;QAACC,OAAOtG;qBAC/B,oBAAClC,oBAAoBuI,QAAQ;QAACC,OAAO5G;qBACnC,oBAACvC,0BAA0BkJ,QAAQ;QACjCC,OAAO;YACLxD;YACAQ;YACA9C;YACAsF;YACAD;QACF;qBAEA,oBAAC5I,iBAAiBoJ,QAAQ;QAACC,OAAO9C;qBAChC,oBAACtG,oBAAoBmJ,QAAQ;QAC3BC,OAAO;YACLC,YAAY7B,MAAM/C,cAAc;YAChCnB;YACA,6BAA6B;YAC7B,8EAA8E;YAC9EnB,KAAKqB;QACP;OAECsF;AAQjB;AAEA,eAAe,SAASQ,UACtBC,KAAgE;IAEhE,MAAM,EAAEC,oBAAoB,EAAE,GAAGC,MAAM,GAAGF;IAE1C,qBACE,oBAACvI;QAAc0I,gBAAgBF;qBAC7B,oBAAC7D,QAAW8D;AAGlB"}