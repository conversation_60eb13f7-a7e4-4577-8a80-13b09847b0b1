/*
 React
 react-server-dom-turbopack-client.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var n=require("util"),q=require("react-dom"),r=require("react"),u={stream:!0};function v(a,b){if(a){var c=a[b[0]];if(a=c[b[2]])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b[2]}return 4===b.length?[a.id,a.chunks,c,1]:[a.id,a.chunks,c]}return b}var w=new Map;
function x(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function y(){}
function aa(a){for(var b=a[1],c=[],d=0;d<b.length;d++){var e=b[d],g=w.get(e);if(void 0===g){g=globalThis.__next_chunk_load__(e);c.push(g);var f=w.set.bind(w,e,null);g.then(f,y);w.set(e,g)}else null!==g&&c.push(g)}return 4===a.length?0===c.length?x(a[0]):Promise.all(c).then(function(){return x(a[0])}):0<c.length?Promise.all(c):null}
function ba(a,b,c){if(null!==a)for(var d=0;d<b.length;d++){var e=c,g=z.current;if(g){var f=g.preinitScript,l=a.prefix+b[d];var k=a.crossOrigin;k="string"===typeof k?"use-credentials"===k?k:"":void 0;f.call(g,l,{crossOrigin:k,nonce:e})}}}
var z=q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,B=Symbol.for("react.element"),ca=Symbol.for("react.provider"),da=Symbol.for("react.server_context"),ea=Symbol.for("react.lazy"),C=Symbol.for("react.default_value"),fa=Symbol.for("react.postpone"),D=Symbol.iterator;function ha(a){if(null===a||"object"!==typeof a)return null;a=D&&a[D]||a["@@iterator"];return"function"===typeof a?a:null}var ia=Array.isArray,E=Object.getPrototypeOf,ja=Object.prototype,G=new WeakMap;
function ka(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function la(a,b,c,d){function e(k,h){if(null===h)return null;if("object"===typeof h){if("function"===typeof h.then){null===l&&(l=new FormData);f++;var t=g++;h.then(function(p){p=JSON.stringify(p,e);var A=l;A.append(b+t,p);f--;0===f&&c(A)},function(p){d(p)});return"$@"+t.toString(16)}if(ia(h))return h;if(h instanceof FormData){null===l&&(l=new FormData);var F=l;k=g++;var m=b+k+"_";h.forEach(function(p,A){F.append(m+A,p)});return"$K"+k.toString(16)}if(h instanceof Map)return h=JSON.stringify(Array.from(h),
e),null===l&&(l=new FormData),k=g++,l.append(b+k,h),"$Q"+k.toString(16);if(h instanceof Set)return h=JSON.stringify(Array.from(h),e),null===l&&(l=new FormData),k=g++,l.append(b+k,h),"$W"+k.toString(16);if(ha(h))return Array.from(h);k=E(h);if(k!==ja&&(null===k||null!==E(k)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return h}if("string"===typeof h){if("Z"===h[h.length-1]&&this[k]instanceof Date)return"$D"+h;
h="$"===h[0]?"$"+h:h;return h}if("boolean"===typeof h)return h;if("number"===typeof h)return ka(h);if("undefined"===typeof h)return"$undefined";if("function"===typeof h){h=G.get(h);if(void 0!==h)return h=JSON.stringify(h,e),null===l&&(l=new FormData),k=g++,l.set(b+k,h),"$F"+k.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof h){k=h.description;if(Symbol.for(k)!==h)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+
(h.description+") cannot be found among global symbols."));return"$S"+k}if("bigint"===typeof h)return"$n"+h.toString(10);throw Error("Type "+typeof h+" is not supported as an argument to a Server Function.");}var g=1,f=0,l=null;a=JSON.stringify(a,e);null===l?c(a):(l.set(b+"0",a),0===f&&c(l))}var H=new WeakMap;
function ma(a){var b,c,d=new Promise(function(e,g){b=e;c=g});la(a,"",function(e){if("string"===typeof e){var g=new FormData;g.append("0",e);e=g}d.status="fulfilled";d.value=e;b(e)},function(e){d.status="rejected";d.reason=e;c(e)});return d}
function na(a){var b=G.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var c=null;if(null!==b.bound){c=H.get(b);c||(c=ma(b),H.set(b,c));if("rejected"===c.status)throw c.reason;if("fulfilled"!==c.status)throw c;b=c.value;var d=new FormData;b.forEach(function(e,g){d.append("$ACTION_"+a+":"+g,e)});c=d;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:c}}
function oa(a,b){var c=G.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(c.id!==a)return!1;var d=c.bound;if(null===d)return 0===b;switch(d.status){case "fulfilled":return d.value.length===b;case "pending":throw d;case "rejected":throw d.reason;default:throw"string"!==typeof d.status&&(d.status="pending",d.then(function(e){d.status="fulfilled";d.value=e},function(e){d.status="rejected";d.reason=e})),d;}}
function I(a,b){Object.defineProperties(a,{$$FORM_ACTION:{value:na},$$IS_SIGNATURE_EQUAL:{value:oa},bind:{value:pa}});G.set(a,b)}var qa=Function.prototype.bind,ra=Array.prototype.slice;function pa(){var a=qa.apply(this,arguments),b=G.get(this);if(b){var c=ra.call(arguments,1),d=null;d=null!==b.bound?Promise.resolve(b.bound).then(function(e){return e.concat(c)}):Promise.resolve(c);I(a,{id:b.id,bound:d})}return a}
function sa(a,b){function c(){var d=Array.prototype.slice.call(arguments);return b(a,d)}I(c,{id:a,bound:null});return c}var J=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function K(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}K.prototype=Object.create(Promise.prototype);
K.prototype.then=function(a,b){switch(this.status){case "resolved_model":L(this);break;case "resolved_module":M(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":case "cyclic":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function ta(a){switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":throw a;default:throw a.reason;}}function N(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function O(a,b,c){switch(a.status){case "fulfilled":N(b,a.value);break;case "pending":case "blocked":case "cyclic":a.value=b;a.reason=c;break;case "rejected":c&&N(c,a.reason)}}
function P(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&N(c,b)}}function Q(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(M(a),O(a,c,d))}}var R=null,S=null;
function L(a){var b=R,c=S;R=a;S=null;var d=a.value;a.status="cyclic";a.value=null;a.reason=null;try{var e=JSON.parse(d,a._response._fromJSON);if(null!==S&&0<S.deps)S.value=e,a.status="blocked",a.value=null,a.reason=null;else{var g=a.value;a.status="fulfilled";a.value=e;null!==g&&N(g,e)}}catch(f){a.status="rejected",a.reason=f}finally{R=b,S=c}}
function M(a){try{var b=a.value,c=globalThis.__next_require__(b[0]);if(4===b.length&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b[2]?c:""===b[2]?c.__esModule?c.default:c:c[b[2]];a.status="fulfilled";a.value=d}catch(e){a.status="rejected",a.reason=e}}function T(a,b){a._chunks.forEach(function(c){"pending"===c.status&&P(c,b)})}function U(a,b){var c=a._chunks,d=c.get(b);d||(d=new K("pending",null,null,a),c.set(b,d));return d}
function ua(a,b,c,d){if(S){var e=S;d||e.deps++}else e=S={deps:d?0:1,value:null};return function(g){b[c]=g;e.deps--;0===e.deps&&"blocked"===a.status&&(g=a.value,a.status="fulfilled",a.value=e.value,null!==g&&N(g,e.value))}}function va(a){return function(b){return P(a,b)}}
function wa(a,b){function c(){var e=Array.prototype.slice.call(arguments),g=b.bound;return g?"fulfilled"===g.status?d(b.id,g.value.concat(e)):Promise.resolve(g).then(function(f){return d(b.id,f.concat(e))}):d(b.id,e)}var d=a._callServer;I(c,b);return c}function V(a,b){a=U(a,b);switch(a.status){case "resolved_model":L(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function xa(a,b,c,d){if("$"===d[0]){if("$"===d)return B;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=U(a,b),{$$typeof:ea,_payload:a,_init:ta};case "@":return b=parseInt(d.slice(2),16),U(a,b);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),J[a]||(b={$$typeof:da,_currentValue:C,_currentValue2:C,_defaultValue:C,_threadCount:0,Provider:null,Consumer:null,_globalName:a},b.Provider={$$typeof:ca,_context:b},J[a]=b),J[a].Provider;case "F":return b=
parseInt(d.slice(2),16),b=V(a,b),wa(a,b);case "Q":return b=parseInt(d.slice(2),16),a=V(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=V(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=U(a,d);switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;
case "pending":case "blocked":case "cyclic":return d=R,a.then(ua(d,b,c,"cyclic"===a.status),va(d)),null;default:throw a.reason;}}}return d}function ya(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}
function za(a,b,c,d){var e=new Map;a={_bundlerConfig:a,_moduleLoading:b,_callServer:void 0!==c?c:ya,_nonce:d,_chunks:e,_stringDecoder:new n.TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=Aa(a);return a}function W(a,b,c){a._chunks.set(b,new K("fulfilled",c,null,a))}
function Ba(a,b,c){var d=a._chunks,e=d.get(b);c=JSON.parse(c,a._fromJSON);var g=v(a._bundlerConfig,c);ba(a._moduleLoading,c[1],a._nonce);if(c=aa(g)){if(e){var f=e;f.status="blocked"}else f=new K("blocked",null,null,a),d.set(b,f);c.then(function(){return Q(f,g)},function(l){return P(f,l)})}else e?Q(e,g):d.set(b,new K("resolved_module",g,null,a))}
function X(a,b){for(var c=a.length,d=b.length,e=0;e<c;e++)d+=a[e].byteLength;d=new Uint8Array(d);for(var g=e=0;g<c;g++){var f=a[g];d.set(f,e);e+=f.byteLength}d.set(b,e);return d}function Y(a,b,c,d,e,g){c=0===c.length&&0===d.byteOffset%g?d:X(c,d);e=new e(c.buffer,c.byteOffset,c.byteLength/g);W(a,b,e)}
function Ca(a,b,c,d,e){switch(c){case 65:W(a,b,X(d,e).buffer);return;case 67:Y(a,b,d,e,Int8Array,1);return;case 99:W(a,b,0===d.length?e:X(d,e));return;case 85:Y(a,b,d,e,Uint8ClampedArray,1);return;case 83:Y(a,b,d,e,Int16Array,2);return;case 115:Y(a,b,d,e,Uint16Array,2);return;case 76:Y(a,b,d,e,Int32Array,4);return;case 108:Y(a,b,d,e,Uint32Array,4);return;case 70:Y(a,b,d,e,Float32Array,4);return;case 68:Y(a,b,d,e,Float64Array,8);return;case 78:Y(a,b,d,e,BigInt64Array,8);return;case 109:Y(a,b,d,e,BigUint64Array,
8);return;case 86:Y(a,b,d,e,DataView,1);return}for(var g=a._stringDecoder,f="",l=0;l<d.length;l++)f+=g.decode(d[l],u);f+=g.decode(e);switch(c){case 73:Ba(a,b,f);break;case 72:b=f[0];f=f.slice(1);a=JSON.parse(f,a._fromJSON);if(f=z.current)switch(b){case "D":f.prefetchDNS(a);break;case "C":"string"===typeof a?f.preconnect(a):f.preconnect(a[0],a[1]);break;case "L":b=a[0];c=a[1];3===a.length?f.preload(b,c,a[2]):f.preload(b,c);break;case "m":"string"===typeof a?f.preloadModule(a):f.preloadModule(a[0],
a[1]);break;case "S":"string"===typeof a?f.preinitStyle(a):f.preinitStyle(a[0],0===a[1]?void 0:a[1],3===a.length?a[2]:void 0);break;case "X":"string"===typeof a?f.preinitScript(a):f.preinitScript(a[0],a[1]);break;case "M":"string"===typeof a?f.preinitModuleScript(a):f.preinitModuleScript(a[0],a[1])}break;case 69:c=JSON.parse(f).digest;f=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
f.stack="Error: "+f.message;f.digest=c;c=a._chunks;(d=c.get(b))?P(d,f):c.set(b,new K("rejected",null,f,a));break;case 84:a._chunks.set(b,new K("fulfilled",f,null,a));break;case 80:f=Error("A Server Component was postponed. The reason is omitted in production builds to avoid leaking sensitive details.");f.$$typeof=fa;f.stack="Error: "+f.message;c=a._chunks;(d=c.get(b))?P(d,f):c.set(b,new K("rejected",null,f,a));break;default:d=a._chunks,(c=d.get(b))?"pending"===c.status&&(a=c.value,b=c.reason,c.status=
"resolved_model",c.value=f,null!==a&&(L(c),O(c,a,b))):d.set(b,new K("resolved_model",f,null,a))}}function Aa(a){return function(b,c){return"string"===typeof c?xa(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===B?{$$typeof:B,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}function Z(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
exports.createFromNodeStream=function(a,b,c){var d=za(b.moduleMap,b.moduleLoading,Z,c&&"string"===typeof c.nonce?c.nonce:void 0);a.on("data",function(e){for(var g=0,f=d._rowState,l=d._rowID,k=d._rowTag,h=d._rowLength,t=d._buffer,F=e.length;g<F;){var m=-1;switch(f){case 0:m=e[g++];58===m?f=1:l=l<<4|(96<m?m-87:m-48);continue;case 1:f=e[g];84===f||65===f||67===f||99===f||85===f||83===f||115===f||76===f||108===f||70===f||68===f||78===f||109===f||86===f?(k=f,f=2,g++):64<f&&91>f?(k=f,f=3,g++):(k=0,f=3);
continue;case 2:m=e[g++];44===m?f=4:h=h<<4|(96<m?m-87:m-48);continue;case 3:m=e.indexOf(10,g);break;case 4:m=g+h,m>e.length&&(m=-1)}var p=e.byteOffset+g;if(-1<m)h=new Uint8Array(e.buffer,p,m-g),Ca(d,l,k,t,h),g=m,3===f&&g++,h=l=k=f=0,t.length=0;else{e=new Uint8Array(e.buffer,p,e.byteLength-g);t.push(e);h-=e.byteLength;break}}d._rowState=f;d._rowID=l;d._rowTag=k;d._rowLength=h});a.on("error",function(e){T(d,e)});a.on("end",function(){T(d,Error("Connection closed."))});return U(d,0)};
exports.createServerReference=function(a){return sa(a,Z)};

//# sourceMappingURL=react-server-dom-turbopack-client.node.production.min.js.map
