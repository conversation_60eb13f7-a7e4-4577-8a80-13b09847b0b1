(()=>{var e={"./dist/compiled/@edge-runtime/cookies/index.js":e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,a={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean);return`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}; ${r.join("; ")}`}function i(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,o]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=o?o:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,o],...a]=i(e),{domain:s,expires:l,httponly:c,maxage:p,path:h,samesite:m,secure:f,priority:g}=Object.fromEntries(a.map(([e,t])=>[e.toLowerCase(),t])),v={name:n,value:decodeURIComponent(o),domain:s,...l&&{expires:new Date(l)},...c&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:h,...m&&{sameSite:u.includes(t=(t=m).toLowerCase())?t:void 0},...f&&{secure:!0},...g&&{priority:d.includes(r=(r=g).toLowerCase())?r:void 0}};return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}(v)}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(a,{RequestCookies:()=>c,ResponseCookies:()=>p,parseCookie:()=>i,parseSetCookie:()=>l,stringifyCookie:()=>s}),e.exports=((e,a,s,i)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let s of n(a))o.call(e,s)||void 0===s||t(e,s,{get:()=>a[s],enumerable:!(i=r(a,s))||i.enumerable});return e})(t({},"__esModule",{value:!0}),a);var u=["strict","lax","none"],d=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t){let e=i(t);for(let[t,r]of e)this._parsed.set(t,{name:t,value:r})}}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let o=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[],a=Array.isArray(o)?o:function(e){if(!e)return[];var t,r,n,o,a,s=[],i=0;function l(){for(;i<e.length&&/\s/.test(e.charAt(i));)i+=1;return i<e.length}for(;i<e.length;){for(t=i,a=!1;l();)if(","===(r=e.charAt(i))){for(n=i,i+=1,l(),o=i;i<e.length&&"="!==(r=e.charAt(i))&&";"!==r&&","!==r;)i+=1;i<e.length&&"="===e.charAt(i)?(a=!0,i=o,s.push(e.substring(t,n)),t=i):i=n+1}else i+=1;(!a||i>=e.length)&&s.push(e.substring(t,e.length))}return s}(o);for(let e of a){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(o,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},"./dist/compiled/cookie/index.js":e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var o={},a=t.split(n),s=(r||{}).decode||e,i=0;i<a.length;i++){var l=a[i],u=l.indexOf("=");if(!(u<0)){var d=l.substr(0,u).trim(),c=l.substr(++u,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==o[d]&&(o[d]=function(e,t){try{return t(e)}catch(t){return e}}(c,s))}}return o},t.serialize=function(e,t,n){var a=n||{},s=a.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var i=s(t);if(i&&!o.test(i))throw TypeError("argument val is invalid");var l=e+"="+i;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(a.domain){if(!o.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!o.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},"./dist/compiled/react-is/cjs/react-is.production.min.js":(e,t)=>{"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),u=Symbol.for("react.context"),d=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case a:case i:case s:case p:case h:return e;default:switch(e=e&&e.$$typeof){case d:case u:case c:case f:case m:case l:return e;default:return t}}case o:return t}}}r=Symbol.for("react.module.reference"),t.ContextConsumer=u,t.ContextProvider=l,t.Element=n,t.ForwardRef=c,t.Fragment=a,t.Lazy=f,t.Memo=m,t.Portal=o,t.Profiler=i,t.StrictMode=s,t.Suspense=p,t.SuspenseList=h,t.isAsyncMode=function(){return!1},t.isConcurrentMode=function(){return!1},t.isContextConsumer=function(e){return v(e)===u},t.isContextProvider=function(e){return v(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return v(e)===c},t.isFragment=function(e){return v(e)===a},t.isLazy=function(e){return v(e)===f},t.isMemo=function(e){return v(e)===m},t.isPortal=function(e){return v(e)===o},t.isProfiler=function(e){return v(e)===i},t.isStrictMode=function(e){return v(e)===s},t.isSuspense=function(e){return v(e)===p},t.isSuspenseList=function(e){return v(e)===h},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===a||e===i||e===s||e===p||e===h||e===g||"object"==typeof e&&null!==e&&(e.$$typeof===f||e.$$typeof===m||e.$$typeof===l||e.$$typeof===u||e.$$typeof===c||e.$$typeof===r||void 0!==e.getModuleId)},t.typeOf=v},"./dist/compiled/react-is/index.js":(e,t,r)=>{"use strict";e.exports=r("./dist/compiled/react-is/cjs/react-is.production.min.js")},"./dist/compiled/strip-ansi/index.js":e=>{(()=>{"use strict";var t={511:e=>{e.exports=({onlyFirst:e=!1}={})=>RegExp("[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))",e?void 0:"g")},532:(e,t,r)=>{let n=r(511);e.exports=e=>"string"==typeof e?e.replace(n(),""):e}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}},s=!0;try{t[e](a,a.exports,n),s=!1}finally{s&&delete r[e]}return a.exports}n.ab=__dirname+"/";var o=n(532);e.exports=o})()},"./dist/esm/build/output/log.js":(e,t,r)=>{"use strict";var n;r.d(t,{ZK:()=>v});let{env:o,stdout:a}=(null==(n=globalThis)?void 0:n.process)??{},s=o&&!o.NO_COLOR&&(o.FORCE_COLOR||(null==a?void 0:a.isTTY)&&!o.CI&&"dumb"!==o.TERM),i=(e,t,r,n)=>{let o=e.substring(0,n)+r,a=e.substring(n+t.length),s=a.indexOf(t);return~s?o+i(a,t,r,s):o+a},l=(e,t,r=e)=>n=>{let o=""+n,a=o.indexOf(t,e.length);return~a?e+i(o,t,r,a)+t:e+o+t},u=s?l("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m"):String;s&&l("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),s&&l("\x1b[3m","\x1b[23m"),s&&l("\x1b[4m","\x1b[24m"),s&&l("\x1b[7m","\x1b[27m"),s&&l("\x1b[8m","\x1b[28m"),s&&l("\x1b[9m","\x1b[29m"),s&&l("\x1b[30m","\x1b[39m");let d=s?l("\x1b[31m","\x1b[39m"):String,c=s?l("\x1b[32m","\x1b[39m"):String,p=s?l("\x1b[33m","\x1b[39m"):String;s&&l("\x1b[34m","\x1b[39m");let h=s?l("\x1b[35m","\x1b[39m"):String;s&&l("\x1b[38;2;173;127;168m","\x1b[39m"),s&&l("\x1b[36m","\x1b[39m");let m=s?l("\x1b[37m","\x1b[39m"):String;s&&l("\x1b[90m","\x1b[39m"),s&&l("\x1b[40m","\x1b[49m"),s&&l("\x1b[41m","\x1b[49m"),s&&l("\x1b[42m","\x1b[49m"),s&&l("\x1b[43m","\x1b[49m"),s&&l("\x1b[44m","\x1b[49m"),s&&l("\x1b[45m","\x1b[49m"),s&&l("\x1b[46m","\x1b[49m"),s&&l("\x1b[47m","\x1b[49m");let f={wait:m(u("○")),error:d(u("⨯")),warn:p(u("⚠")),ready:"▲",info:m(u(" ")),event:c(u("✓")),trace:h(u("\xbb"))},g={log:"log",warn:"warn",error:"error"};function v(...e){!function(e,...t){(""===t[0]||void 0===t[0])&&1===t.length&&t.shift();let r=e in g?g[e]:"log",n=f[e];0===t.length?console[r](""):console[r](" "+n,...t)}("warn",...e)}},"./dist/esm/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{BR:()=>a,Ei:()=>u,Eo:()=>p,Lx:()=>c,Qq:()=>o,Wo:()=>i,lk:()=>h,oL:()=>l,q6:()=>d,wh:()=>s,y3:()=>n});let n="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",a=31536e3,s="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",i="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",l="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",u="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",d="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",c="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",p="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",h="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",m={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"};({...m,GROUP:{server:[m.reactServerComponents,m.actionBrowser,m.appMetadataRoute,m.appRouteHandler],nonClientServerTarget:[m.middleware,m.api],app:[m.reactServerComponents,m.actionBrowser,m.appMetadataRoute,m.appRouteHandler,m.serverSideRendering,m.appPagesBrowser]}})},"./dist/esm/server/api-utils/index.js":(e,t,r)=>{"use strict";r.d(t,{Di:()=>l,Iq:()=>a,Lm:()=>d,QM:()=>i,dS:()=>s,gk:()=>c});var n=r("./dist/esm/server/web/spec-extension/adapters/headers.js"),o=r("./dist/esm/lib/constants.js");function a(e,t){let r=n.h.from(e.headers),a=r.get(o.y3),s=a===t.previewModeId,i=r.has(o.Qq);return{isOnDemandRevalidate:s,revalidateOnlyGenerated:i}}let s="__prerender_bypass",i="__next_preview_data",l=Symbol(i),u=Symbol(s);function d(e,t={}){if(u in e)return e;let{serialize:n}=r("./dist/compiled/cookie/index.js"),o=e.getHeader("Set-Cookie");return e.setHeader("Set-Cookie",[..."string"==typeof o?[o]:Array.isArray(o)?o:[],n(s,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==t.path?{path:t.path}:void 0}),n(i,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==t.path?{path:t.path}:void 0})]),Object.defineProperty(e,u,{value:!0,enumerable:!1}),e}function c({req:e},t,r){let n={configurable:!0,enumerable:!0},o={...n,writable:!0};Object.defineProperty(e,t,{...n,get:()=>{let n=r();return Object.defineProperty(e,t,{...o,value:n}),n},set:r=>{Object.defineProperty(e,t,{...o,value:r})}})}},"./dist/esm/server/api-utils/node/try-get-preview-data.js":(e,t,r)=>{"use strict";r.d(t,{R:()=>s});var n=r("./dist/esm/server/api-utils/index.js"),o=r("./dist/esm/server/web/spec-extension/cookies.js"),a=r("./dist/esm/server/web/spec-extension/adapters/headers.js");function s(e,t,s){var i,l;let u;if(s&&(0,n.Iq)(e,s).isOnDemandRevalidate)return!1;if(n.Di in e)return e[n.Di];let d=a.h.from(e.headers),c=new o.q(d),p=null==(i=c.get(n.dS))?void 0:i.value,h=null==(l=c.get(n.QM))?void 0:l.value;if(p&&!h&&p===s.previewModeId){let t={};return Object.defineProperty(e,n.Di,{value:t,enumerable:!1}),t}if(!p&&!h)return!1;if(!p||!h||p!==s.previewModeId)return(0,n.Lm)(t),!1;try{let e=r("next/dist/compiled/jsonwebtoken");u=e.verify(h,s.previewModeSigningKey)}catch{return(0,n.Lm)(t),!1}let{decryptWithSecret:m}=r("./dist/esm/server/crypto-utils.js"),f=m(Buffer.from(s.previewModeEncryptionKey),u.data);try{let t=JSON.parse(f);return Object.defineProperty(e,n.Di,{value:t,enumerable:!1}),t}catch{return!1}}},"./dist/esm/server/crypto-utils.js":(e,t,r)=>{"use strict";r.r(t),r.d(t,{decryptWithSecret:()=>i,encryptWithSecret:()=>s});let n=require("crypto");var o=r.n(n);let a="aes-256-gcm";function s(e,t){let r=o().randomBytes(16),n=o().randomBytes(64),s=o().pbkdf2Sync(e,n,1e5,32,"sha512"),i=o().createCipheriv(a,s,r),l=Buffer.concat([i.update(t,"utf8"),i.final()]),u=i.getAuthTag();return Buffer.concat([n,r,u,l]).toString("hex")}function i(e,t){let r=Buffer.from(t,"hex"),n=r.slice(0,64),s=r.slice(64,80),i=r.slice(80,96),l=r.slice(96),u=o().pbkdf2Sync(e,n,1e5,32,"sha512"),d=o().createDecipheriv(a,u,s);return d.setAuthTag(i),d.update(l)+d.final("utf8")}},"./dist/esm/server/optimize-amp.js":(e,t,r)=>{"use strict";async function n(e,t){let n;try{n=r("next/dist/compiled/@ampproject/toolbox-optimizer")}catch(t){return e}let o=n.create(t);return o.transformHtml(e,t)}r.d(t,{Z:()=>n})},"./dist/esm/server/post-process.js":(e,t,r)=>{"use strict";r.d(t,{X:()=>l});var n,o=r("./dist/esm/shared/lib/constants.js");function a(e){return null!=e}let s=[];async function i(e,t,n){if(!s[0])return e;let{parse:o}=r("next/dist/compiled/node-html-parser"),a=o(e),i=e;async function l(e){let r=e.inspect(a,t);i=await e.mutate(i,r,t)}for(let e=0;e<s.length;e++){let t=s[e];(!t.condition||t.condition(n))&&await l(s[e].middleware)}return i}async function l(e,t,n,{inAmpMode:o,hybridAmp:s}){let l=[o?async t=>{let o=r("./dist/esm/server/optimize-amp.js").Z;return t=await o(t,n.ampOptimizerConfig),!n.ampSkipValidation&&n.ampValidator&&await n.ampValidator(t,e),t}:null,(0,n.optimizeFonts)?async e=>await i(e,{getFontDefinition:e=>{var t;return n.fontManifest&&(null==(t=n.fontManifest.find(t=>!!t&&t.url===e))?void 0:t.content)||""}},{optimizeFonts:n.optimizeFonts}):null,(0,n.optimizeCss)?async e=>{let t=r("critters"),o=new t({ssrMode:!0,reduceInlineStyles:!1,path:n.distDir,publicPath:`${n.assetPrefix}/_next/`,preload:"media",fonts:!1,...n.optimizeCss});return await o.process(e)}:null,o||s?e=>e.replace(/&amp;amp=1/g,"&amp=1"):null].filter(a);for(let e of l)e&&(t=await e(t));return t}n=new class{inspect(e,t){if(!t.getFontDefinition)return;let r=[];return e.querySelectorAll("link").filter(e=>"stylesheet"===e.getAttribute("rel")&&e.hasAttribute("data-href")&&o.C7.some(({url:t})=>{let r=e.getAttribute("data-href");return!!r&&r.startsWith(t)})).forEach(e=>{let t=e.getAttribute("data-href"),n=e.getAttribute("nonce");t&&r.push([t,n])}),r}constructor(){this.mutate=async(e,t,r)=>{let n=e,a=new Set;if(!r.getFontDefinition)return e;t.forEach(e=>{let[t,s]=e,i=`<link rel="stylesheet" href="${t}"/>`;if(n.indexOf(`<style data-href="${t}">`)>-1||n.indexOf(i)>-1)return;let l=r.getFontDefinition?r.getFontDefinition(t):null;if(l){let e=s?` nonce="${s}"`:"",r="";l.includes("ascent-override")&&(r=' data-size-adjust="true"'),n=n.replace("</head>",`<style data-href="${t}"${e}${r}>${l}</style></head>`);let i=t.replace(/&/g,"&amp;").replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),u=RegExp(`<link[^>]*data-href="${i}"[^>]*/>`);n=n.replace(u,"");let d=o.C7.find(e=>t.startsWith(e.url));d&&a.add(d.preconnect)}else n=n.replace("</head>",`${i}</head>`)});let s="";return a.forEach(e=>{s+=`<link rel="preconnect" href="${e}" crossorigin />`}),n=n.replace('<meta name="next-font-preconnect"/>',s)}}},s.push({name:"Inline-Fonts",middleware:n,condition:(e=>e.optimizeFonts||process.env.__NEXT_OPTIMIZE_FONTS)||null})},"./dist/esm/server/web/spec-extension/adapters/headers.js":(e,t,r)=>{"use strict";r.d(t,{h:()=>a});var n=r("./dist/esm/server/web/spec-extension/adapters/reflect.js");class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class a extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.g.get(t,r,o);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==s)return n.g.get(t,s,o)},set(t,r,o,a){if("symbol"==typeof r)return n.g.set(t,r,o,a);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);return n.g.set(t,i??r,o,a)},has(t,r){if("symbol"==typeof r)return n.g.has(t,r);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==a&&n.g.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return n.g.deleteProperty(t,r);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===a||n.g.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.g.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new a(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},"./dist/esm/server/web/spec-extension/adapters/reflect.js":(e,t,r)=>{"use strict";r.d(t,{g:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},"./dist/esm/server/web/spec-extension/cookies.js":(e,t,r)=>{"use strict";r.d(t,{q:()=>n.RequestCookies});var n=r("./dist/compiled/@edge-runtime/cookies/index.js")},"./dist/esm/shared/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{C7:()=>i,Er:()=>l,NO:()=>a,uY:()=>s,wU:()=>o}),r("./dist/esm/shared/lib/modern-browserslist-target.js");let n={client:"client",server:"server",edgeServer:"edge-server"};n.client,n.server,n.edgeServer;let o="__NEXT_BUILTIN_DOCUMENT__";Symbol("polyfills");let a="__N_SSG",s="__N_SSP",i=[{url:"https://fonts.googleapis.com/",preconnect:"https://fonts.gstatic.com"},{url:"https://use.typekit.net",preconnect:"https://use.typekit.net"}],l=["/500"]},"./dist/esm/shared/lib/modern-browserslist-target.js":e=>{e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},critters:e=>{"use strict";e.exports=require("critters")},"next/dist/compiled/@ampproject/toolbox-optimizer":e=>{"use strict";e.exports=require("next/dist/compiled/@ampproject/toolbox-optimizer")},"next/dist/compiled/@next/react-dev-overlay/dist/middleware":e=>{"use strict";e.exports=require("next/dist/compiled/@next/react-dev-overlay/dist/middleware")},"next/dist/compiled/jsonwebtoken":e=>{"use strict";e.exports=require("next/dist/compiled/jsonwebtoken")},"next/dist/compiled/node-html-parser":e=>{"use strict";e.exports=require("next/dist/compiled/node-html-parser")},path:e=>{"use strict";e.exports=require("path")}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";let e,t,o;r.r(n),r.d(n,{PagesRouteModule:()=>te,default:()=>tr,renderToHTML:()=>e6,vendored:()=>tt});var a,s,i,l,u,d,c,p,h,m,f,g,v,y={};r.r(y),r.d(y,{AmpStateContext:()=>B});var b={};r.r(b),r.d(b,{HeadManagerContext:()=>U});var x={};r.r(x),r.d(x,{LoadableContext:()=>W});var w={};r.r(w),r.d(w,{default:()=>X});var S={};r.r(S),r.d(S,{RouterContext:()=>K});var P={};r.r(P),r.d(P,{HtmlContext:()=>eu,useHtmlContext:()=>ed});var _={};r.r(_),r.d(_,{ImageConfigContext:()=>eM});var E={};r.r(E),r.d(E,{PathParamsContext:()=>eB,PathnameContext:()=>ez,SearchParamsContext:()=>eH});var R={};r.r(R),r.d(R,{AppRouterContext:()=>eV,CacheStates:()=>v,GlobalLayoutRouterContext:()=>eQ,LayoutRouterContext:()=>eY,TemplateContext:()=>eX});var C={};r.r(C),r.d(C,{ServerInsertedHTMLContext:()=>e5,useServerInsertedHTML:()=>e7});var T={};r.r(T),r.d(T,{AmpContext:()=>y,AppRouterContext:()=>R,HeadManagerContext:()=>b,HooksClientContext:()=>E,HtmlContext:()=>P,ImageConfigContext:()=>_,Loadable:()=>w,LoadableContext:()=>x,RouterContext:()=>S,ServerInsertedHtml:()=>C});class j{constructor({userland:e,definition:t}){this.userland=e,this.definition=t}}var $=r("./dist/esm/server/api-utils/index.js");let A=require("react");var L=r.n(A);let N=require("react-dom/server.browser");var O=r.n(N);let I=require("styled-jsx");var k=r("./dist/esm/lib/constants.js"),M=r("./dist/esm/shared/lib/constants.js");function D(e){return Object.prototype.toString.call(e)}function q(e){if("[object Object]"!==D(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}let F=/^[A-Za-z_$][A-Za-z0-9_$]*$/;class H extends Error{constructor(e,t,r,n){super(r?`Error serializing \`${r}\` returned from \`${t}\` in "${e}".
Reason: ${n}`:`Error serializing props returned from \`${t}\` in "${e}".
Reason: ${n}`)}}function z(e,t,r){if(!q(r))throw new H(e,t,"",`Props must be returned as a plain object from ${t}: \`{ props: { ... } }\` (received: \`${D(r)}\`).`);function n(r,n,o){if(r.has(n))throw new H(e,t,o,`Circular references cannot be expressed in JSON (references: \`${r.get(n)||"(self)"}\`).`);r.set(n,o)}return function r(o,a,s){let i=typeof a;if(null===a||"boolean"===i||"number"===i||"string"===i)return!0;if("undefined"===i)throw new H(e,t,s,"`undefined` cannot be serialized as JSON. Please use `null` or omit this value.");if(q(a)){if(n(o,a,s),Object.entries(a).every(([e,t])=>{let n=F.test(e)?`${s}.${e}`:`${s}[${JSON.stringify(e)}]`,a=new Map(o);return r(a,e,n)&&r(a,t,n)}))return!0;throw new H(e,t,s,"invariant: Unknown error encountered in Object.")}if(Array.isArray(a)){if(n(o,a,s),a.every((e,t)=>{let n=new Map(o);return r(n,e,`${s}[${t}]`)}))return!0;throw new H(e,t,s,"invariant: Unknown error encountered in Array.")}throw new H(e,t,s,"`"+i+"`"+("object"===i?` ("${Object.prototype.toString.call(a)}")`:"")+" cannot be serialized as JSON. Please only return JSON serializable data types.")}(new Map,r,"")}let B=L().createContext({}),U=L().createContext({}),W=L().createContext(null),J=[],Z=[];function G(e){let t=e(),r={loading:!0,loaded:null,error:null};return r.promise=t.then(e=>(r.loading=!1,r.loaded=e,e)).catch(e=>{throw r.loading=!1,r.error=e,e}),r}class V{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},t.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}}function Y(e){return function(e,t){let r=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),n=null;function o(){if(!n){let t=new V(e,r);n={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return n.promise()}function a(e,t){!function(){o();let e=L().useContext(W);e&&Array.isArray(r.modules)&&r.modules.forEach(t=>{e(t)})}();let a=L().useSyncExternalStore(n.subscribe,n.getCurrentValue,n.getCurrentValue);return L().useImperativeHandle(t,()=>({retry:n.retry}),[]),L().useMemo(()=>{var t;return a.loading||a.error?L().createElement(r.loading,{isLoading:a.loading,pastDelay:a.pastDelay,timedOut:a.timedOut,error:a.error,retry:n.retry}):a.loaded?L().createElement((t=a.loaded)&&t.default?t.default:t,e):null},[e,a])}return J.push(o),a.preload=()=>o(),a.displayName="LoadableComponent",L().forwardRef(a)}(G,e)}function Q(e,t){let r=[];for(;e.length;){let n=e.pop();r.push(n(t))}return Promise.all(r).then(()=>{if(e.length)return Q(e,t)})}Y.preloadAll=()=>new Promise((e,t)=>{Q(J).then(e,t)}),Y.preloadReady=e=>(void 0===e&&(e=[]),new Promise(t=>{let r=()=>t();Q(Z,e).then(r,r)}));let X=Y,K=L().createContext(null);function ee(e){return e.startsWith("/")?e:"/"+e}let et=["(..)(..)","(.)","(..)","(...)"],er=/\/\[[^/]+?\](?=\/|$)/;function en(e){return void 0!==e.split("/").find(e=>et.find(t=>e.startsWith(t)))&&(e=function(e){let t,r,n;for(let o of e.split("/"))if(r=et.find(e=>o.startsWith(e))){[t,n]=e.split(r,2);break}if(!t||!r||!n)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=ee(t.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")),r){case"(.)":n="/"===t?`/${n}`:t+"/"+n;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);n=t.split("/").slice(0,-1).concat(n).join("/");break;case"(...)":n="/"+n;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);n=o.slice(0,-2).concat(n).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:n}}(e).interceptedRoute),er.test(e)}function eo(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function ea(e){return e.finished||e.headersSent}async function es(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await es(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&ea(r))return n;if(!n){let t='"'+eo(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.';throw Error(t)}return n}let ei="undefined"!=typeof performance;ei&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class el extends Error{}let eu=(0,A.createContext)(void 0);function ed(){let e=(0,A.useContext)(eu);if(!e)throw Error("<Html> should not be imported outside of pages/_document.\nRead more: https://nextjs.org/docs/messages/no-document-import-in-page");return e}let ec=Symbol.for("NextInternalRequestMeta");function ep(e,t){let r=e[ec]||{};return"string"==typeof t?r[t]:r}!function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(a||(a={}));let eh=new Set([301,302,303,307,308]);function em(e){return e.statusCode||(e.permanent?a.PermanentRedirect:a.TemporaryRedirect)}let ef=require("next/dist/server/lib/trace/tracer");(function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"})(s||(s={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(i||(i={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(l||(l={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(u||(u={})),(d||(d={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(c||(c={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(p||(p={})),(h||(h={})).executeRoute="Router.executeRoute",(m||(m={})).runHandler="Node.runHandler",(f||(f={})).runHandler="AppRouteRouteHandlers.runHandler",function(e){e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport"}(g||(g={}));class eg{constructor(){let e,t;this.promise=new Promise((r,n)=>{e=r,t=n}),this.resolve=e,this.reject=t}}let ev=e=>{setImmediate(e)};function ey(...e){let{readable:t,writable:r}=new TransformStream,n=Promise.resolve();for(let t=0;t<e.length;++t)n=n.then(()=>e[t].pipeTo(r,{preventClose:t+1<e.length}));return n.catch(()=>{}),t}function eb(e){let t=new TextEncoder;return new ReadableStream({start(r){r.enqueue(t.encode(e)),r.close()}})}async function ex(e){let t="";return await e.pipeThrough(function(e=new TextDecoder){return new TransformStream({transform:(t,r)=>r.enqueue(e.decode(t,{stream:!0})),flush:t=>t.enqueue(e.decode())})}()).pipeTo(new WritableStream({write(e){t+=e}})),t}async function ew(e,{suffix:t,inlinedDataStream:r,isStaticGeneration:n,getServerInsertedHTML:o,serverInsertedHTMLToHead:a,validateRootLayout:s}){let i="</body></html>",l=t?t.split(i,1)[0]:null;return n&&"allReady"in e&&await e.allReady,function(e,t){let r=e;for(let e of t)e&&(r=r.pipeThrough(e));return r}(e,[function(){let e,t=new Uint8Array,r=r=>{if(e)return;let n=new eg;e=n,ev(()=>{try{r.enqueue(t),t=new Uint8Array}catch{}finally{e=void 0,n.resolve()}})};return new TransformStream({transform(e,n){let o=new Uint8Array(t.length+e.byteLength);o.set(t),o.set(e,t.length),t=o,r(n)},flush(){if(e)return e.promise}})}(),o&&!a?function(e){let t=new TextEncoder;return new TransformStream({transform:async(r,n)=>{let o=await e();o&&n.enqueue(t.encode(o)),n.enqueue(r)}})}(o):null,null!=l&&l.length>0?function(e){let t,r=!1,n=new TextEncoder,o=r=>{let o=new eg;t=o,ev(()=>{try{r.enqueue(n.encode(e))}catch{}finally{t=void 0,o.resolve()}})};return new TransformStream({transform(e,t){t.enqueue(e),r||(r=!0,o(t))},flush(o){if(t)return t.promise;r||o.enqueue(n.encode(e))}})}(l):null,r?function(e){let t=!1,r=null,n=t=>{let n=e.getReader(),o=new eg;r=o,ev(async()=>{try{for(;;){let{done:e,value:r}=await n.read();if(e)return;t.enqueue(r)}}catch(e){t.error(e)}finally{o.resolve()}})};return new TransformStream({transform(e,r){r.enqueue(e),t||(t=!0,n(r))},flush(){if(r&&t)return r.promise}})}(r):null,function(e){let t=!1,r=new TextEncoder,n=new TextDecoder;return new TransformStream({transform(o,a){if(t)return a.enqueue(o);let s=n.decode(o),i=s.indexOf(e);if(i>-1){if(t=!0,s.length===e.length)return;let n=s.slice(0,i);if(o=r.encode(n),a.enqueue(o),s.length>e.length+i){let t=s.slice(i+e.length);o=r.encode(t),a.enqueue(o)}}else a.enqueue(o)},flush(t){t.enqueue(r.encode(e))}})}(i),o&&a?function(e){let t=!1,r=!1,n=new TextEncoder,o=new TextDecoder;return new TransformStream({async transform(a,s){if(r){s.enqueue(a);return}let i=await e();if(t)s.enqueue(n.encode(i)),s.enqueue(a),r=!0;else{let e=o.decode(a),l=e.indexOf("</head>");if(-1!==l){let o=e.slice(0,l)+i+e.slice(l);s.enqueue(n.encode(o)),r=!0,t=!0}}t?ev(()=>{r=!1}):s.enqueue(a)},async flush(t){let r=await e();r&&t.enqueue(n.encode(r))}})}(o):null,s?function(e="",t){let r=!1,n=!1,o=new TextEncoder,a=new TextDecoder,s="";return new TransformStream({async transform(e,t){(!r||!n)&&(s+=a.decode(e,{stream:!0}),!r&&s.includes("<html")&&(r=!0),!n&&s.includes("<body")&&(n=!0)),t.enqueue(e)},flush(i){(!r||!n)&&(s+=a.decode(),!r&&s.includes("<html")&&(r=!0),!n&&s.includes("<body")&&(n=!0));let l=[];r||l.push("html"),n||l.push("body"),l.length>0&&i.enqueue(o.encode(`<script>self.__next_root_layout_missing_tags_error=${JSON.stringify({missingTags:l,assetPrefix:e??"",tree:t()})}</script>`))}})}(s.assetPrefix,s.getTree):null])}function eS(e){return e.replace(/\/$/,"")||"/"}function eP(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function e_(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:o}=eP(e);return""+t+r+n+o}function eE(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:o}=eP(e);return""+r+t+n+o}function eR(e,t){if("string"!=typeof e)return!1;let{pathname:r}=eP(e);return r===t||r.startsWith(t+"/")}function eC(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}let eT=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function ej(e,t){return new URL(String(e).replace(eT,"localhost"),t&&String(t).replace(eT,"localhost"))}let e$=Symbol("NextURLInternal");class eA{constructor(e,t,r){let n,o;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,o=r||{}):o=r||t||{},this[e$]={url:ej(e,n??o.base),options:o,basePath:""},this.analyze()}analyze(){var e,t,r,n,o;let a=function(e,t){var r,n;let{basePath:o,i18n:a,trailingSlash:s}=null!=(r=t.nextConfig)?r:{},i={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):s};o&&eR(i.pathname,o)&&(i.pathname=function(e,t){if(!eR(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(i.pathname,o),i.basePath=o);let l=i.pathname;if(i.pathname.startsWith("/_next/data/")&&i.pathname.endsWith(".json")){let e=i.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];i.buildId=r,l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(i.pathname=l)}if(a){let e=t.i18nProvider?t.i18nProvider.analyze(i.pathname):eC(i.pathname,a.locales);i.locale=e.detectedLocale,i.pathname=null!=(n=e.pathname)?n:i.pathname,!e.detectedLocale&&i.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):eC(l,a.locales)).detectedLocale&&(i.locale=e.detectedLocale)}return i}(this[e$].url.pathname,{nextConfig:this[e$].options.nextConfig,parseData:!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,i18nProvider:this[e$].options.i18nProvider}),s=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[e$].url,this[e$].options.headers);this[e$].domainLocale=this[e$].options.i18nProvider?this[e$].options.i18nProvider.detectDomainLocale(s):function(e,t,r){if(e)for(let a of(r&&(r=r.toLowerCase()),e)){var n,o;let e=null==(n=a.domain)?void 0:n.split(":",1)[0].toLowerCase();if(t===e||r===a.defaultLocale.toLowerCase()||(null==(o=a.locales)?void 0:o.some(e=>e.toLowerCase()===r)))return a}}(null==(t=this[e$].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,s);let i=(null==(r=this[e$].domainLocale)?void 0:r.defaultLocale)||(null==(o=this[e$].options.nextConfig)?void 0:null==(n=o.i18n)?void 0:n.defaultLocale);this[e$].url.pathname=a.pathname,this[e$].defaultLocale=i,this[e$].basePath=a.basePath??"",this[e$].buildId=a.buildId,this[e$].locale=a.locale??i,this[e$].trailingSlash=a.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let o=e.toLowerCase();return!n&&(eR(o,"/api")||eR(o,"/"+t.toLowerCase()))?e:e_(e,"/"+t)}((e={basePath:this[e$].basePath,buildId:this[e$].buildId,defaultLocale:this[e$].options.forceLocale?void 0:this[e$].defaultLocale,locale:this[e$].locale,pathname:this[e$].url.pathname,trailingSlash:this[e$].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=eS(t)),e.buildId&&(t=eE(e_(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=e_(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:eE(t,"/"):eS(t)}formatSearch(){return this[e$].url.search}get buildId(){return this[e$].buildId}set buildId(e){this[e$].buildId=e}get locale(){return this[e$].locale??""}set locale(e){var t,r;if(!this[e$].locale||!(null==(r=this[e$].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[e$].locale=e}get defaultLocale(){return this[e$].defaultLocale}get domainLocale(){return this[e$].domainLocale}get searchParams(){return this[e$].url.searchParams}get host(){return this[e$].url.host}set host(e){this[e$].url.host=e}get hostname(){return this[e$].url.hostname}set hostname(e){this[e$].url.hostname=e}get port(){return this[e$].url.port}set port(e){this[e$].url.port=e}get protocol(){return this[e$].url.protocol}set protocol(e){this[e$].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[e$].url=ej(e),this.analyze()}get origin(){return this[e$].url.origin}get pathname(){return this[e$].url.pathname}set pathname(e){this[e$].url.pathname=e}get hash(){return this[e$].url.hash}set hash(e){this[e$].url.hash=e}get search(){return this[e$].url.search}set search(e){this[e$].url.search=e}get password(){return this[e$].url.password}set password(e){this[e$].url.password=e}get username(){return this[e$].url.username}set username(e){this[e$].url.username=e}get basePath(){return this[e$].basePath}set basePath(e){this[e$].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new eA(String(this),this[e$].options)}}r("./dist/esm/server/web/spec-extension/cookies.js"),Symbol("internal request"),Request,Symbol.for("edge-runtime.inspect.custom");let eL="ResponseAborted";class eN extends Error{constructor(...e){super(...e),this.name=eL}}function eO(e){return(null==e?void 0:e.name)==="AbortError"||(null==e?void 0:e.name)===eL}async function eI(e,t,r){try{let{errored:n,destroyed:o}=t;if(n||o)return;let a=function(e){let t=new AbortController;return e.once("close",()=>{e.writableFinished||t.abort(new eN)}),t}(t),s=function(e,t){let r=!1,n=new eg;function o(){n.resolve()}e.on("drain",o),e.once("close",()=>{e.off("drain",o),n.resolve()});let a=new eg;return e.once("finish",()=>{a.resolve()}),new WritableStream({write:async t=>{r||(r=!0,e.flushHeaders());try{let r=e.write(t);"flush"in e&&"function"==typeof e.flush&&e.flush(),r||(await n.promise,n=new eg)}catch(t){throw e.end(),Error("failed to write chunk to response",{cause:t})}},abort:t=>{e.writableFinished||e.destroy(t)},close:async()=>{if(t&&await t,!e.writableFinished)return e.end(),a.promise}})}(t,r);await e.pipeTo(s,{signal:a.signal})}catch(e){if(eO(e))return;throw Error("failed to pipe response",{cause:e})}}class ek{static fromStatic(e){return new ek(e,{metadata:{}})}constructor(e,{contentType:t,waitUntil:r,metadata:n}){this.response=e,this.contentType=t,this.metadata=n,this.waitUntil=r}assignMetadata(e){Object.assign(this.metadata,e)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedString(e=!1){if(null===this.response)throw Error("Invariant: null responses cannot be unchunked");if("string"!=typeof this.response){if(!e)throw Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js");return ex(this.readable)}return this.response}get readable(){if(null===this.response)throw Error("Invariant: null responses cannot be streamed");if("string"==typeof this.response)throw Error("Invariant: static responses cannot be streamed");return Array.isArray(this.response)?ey(...this.response):this.response}chain(e){let t;if(null===this.response)throw Error("Invariant: response is null. This is a bug in Next.js");(t="string"==typeof this.response?[eb(this.response)]:Array.isArray(this.response)?this.response:[this.response]).push(e),this.response=t}async pipeTo(e){try{await this.readable.pipeTo(e,{preventClose:!0}),this.waitUntil&&await this.waitUntil,await e.close()}catch(t){if(eO(t)){await e.abort(t);return}throw t}}async pipeToNodeResponse(e){await eI(this.readable,e,this.waitUntil)}}let eM=L().createContext({deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1});var eD=r("./dist/compiled/strip-ansi/index.js"),eq=r.n(eD);let eF=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],eH=(0,A.createContext)(null),ez=(0,A.createContext)(null),eB=(0,A.createContext)(null),eU=/[|\\{}()[\]^$+*?.-]/,eW=/[|\\{}()[\]^$+*?.-]/g;function eJ(e){return eU.test(e)?e.replace(eW,"\\$&"):e}function eZ(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function eG(e){let{children:t,router:r,...n}=e,o=(0,A.useRef)(n.isAutoExport),a=(0,A.useMemo)(()=>{let e;let t=o.current;if(t&&(o.current=!1),en(r.pathname)&&(r.isFallback||t&&!r.isReady))return null;try{e=new URL(r.asPath,"http://f")}catch(e){return"/"}return e.pathname},[r.asPath,r.isFallback,r.isReady,r.pathname]);return L().createElement(ez.Provider,{value:a},t)}!function(e){e.LAZY_INITIALIZED="LAZYINITIALIZED",e.DATA_FETCH="DATAFETCH",e.READY="READY"}(v||(v={}));let eV=L().createContext(null),eY=L().createContext(null),eQ=L().createContext(null),eX=L().createContext(null),eK="<!DOCTYPE html>";function e0(){throw Error('No router instance found. you should only use "next/router" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance')}async function e1(e){let t=await O().renderToReadableStream(e);return await t.allReady,ex(t)}e=r("./dist/esm/server/api-utils/node/try-get-preview-data.js").R,t=r("./dist/esm/build/output/log.js").ZK,o=r("./dist/esm/server/post-process.js").X;class e4{constructor(e,t,r,{isFallback:n},o,a,s,i,l,u,d,c){this.route=e.replace(/\/$/,"")||"/",this.pathname=e,this.query=t,this.asPath=r,this.isFallback=n,this.basePath=a,this.locale=s,this.locales=i,this.defaultLocale=l,this.isReady=o,this.domainLocales=u,this.isPreview=!!d,this.isLocaleDomain=!!c}push(){e0()}replace(){e0()}reload(){e0()}back(){e0()}forward(){e0()}prefetch(){e0()}beforePopState(){e0()}}function e3(e,t,r){return L().createElement(e,{Component:t,...r})}let e2=(e,t)=>{let r=`invalid-${e.toLocaleLowerCase()}-value`;return`Additional keys were returned from \`${e}\`. Properties intended for your component must be nested under the \`props\` key, e.g.:

	return { props: { title: 'My Title', content: '...' } }

Keys that need to be moved: ${t.join(", ")}.
Read more: https://nextjs.org/docs/messages/${r}`};function e8(e,t,r){let{destination:n,permanent:o,statusCode:a,basePath:s}=e,i=[],l=void 0!==a,u=void 0!==o;u&&l?i.push("`permanent` and `statusCode` can not both be provided"):u&&"boolean"!=typeof o?i.push("`permanent` must be `true` or `false`"):l&&!eh.has(a)&&i.push(`\`statusCode\` must undefined or one of ${[...eh].join(", ")}`);let d=typeof n;"string"!==d&&i.push(`\`destination\` should be string but received ${d}`);let c=typeof s;if("undefined"!==c&&"boolean"!==c&&i.push(`\`basePath\` should be undefined or a false, received ${c}`),i.length>0)throw Error(`Invalid redirect object returned from ${r} for ${t.url}
`+i.join(" and ")+"\nSee more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp")}async function e9(n,a,s,i,l,u){var d,h;let m,f,g;(0,$.gk)({req:n},"cookies",(h=n.headers,function(){let{cookie:e}=h;if(!e)return{};let{parse:t}=r("./dist/compiled/cookie/index.js");return t(Array.isArray(e)?e.join("; "):e)}));let v={};v.assetQueryString=l.dev?l.assetQueryString||`?ts=${Date.now()}`:"",l.deploymentId&&(v.assetQueryString+=`${v.assetQueryString?"&":"?"}dpl=${l.deploymentId}`),i=Object.assign({},i);let{err:y,dev:b=!1,ampPath:x="",pageConfig:w={},buildManifest:S,reactLoadableManifest:P,ErrorDebug:_,getStaticProps:E,getStaticPaths:R,getServerSideProps:C,isDataReq:T,params:j,previewProps:A,basePath:N,images:D,runtime:q,isExperimentalCompile:F}=l,{App:H}=u,J=v.assetQueryString,Z=u.Document,G=l.Component,V=!!i.__nextFallback,Y=i.__nextNotFoundSrcPage;!function(e){for(let t of eF)delete e[t]}(i);let Q=!!E,er=Q&&l.nextExport,ei=H.getInitialProps===H.origGetInitialProps,ed=!!(null==G?void 0:G.getInitialProps),ec=null==G?void 0:G.unstable_scriptLoader,eh=en(s),eg="/_error"===s&&G.getInitialProps===G.origGetInitialProps;l.nextExport&&ed&&!eg&&t(`Detected getInitialProps on page '${s}' while running export. It's recommended to use getStaticProps which has a more correct behavior for static exporting.
Read more: https://nextjs.org/docs/messages/get-initial-props-export`);let ev=!ed&&ei&&!Q&&!C;if(ev&&!b&&F&&(a.setHeader("Cache-Control","number"==typeof!1?"s-maxage=false, stale-while-revalidate":`s-maxage=${k.BR}, stale-while-revalidate`),ev=!1),ed&&Q)throw Error(k.wh+` ${s}`);if(ed&&C)throw Error(k.Wo+` ${s}`);if(C&&Q)throw Error(k.oL+` ${s}`);if(C&&"export"===l.nextConfigOutput)throw Error('getServerSideProps cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if(R&&!eh)throw Error(`getStaticPaths is only allowed for dynamic SSG pages and was found on '${s}'.
Read more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`);if(R&&!Q)throw Error(`getStaticPaths was added without a getStaticProps in ${s}. Without getStaticProps, getStaticPaths does nothing`);if(Q&&eh&&!R)throw Error(`getStaticPaths is required for dynamic SSG pages and is missing for '${s}'.
Read more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`);let eP=l.resolvedAsPath||n.url;if(b){let{isValidElementType:e}=r("./dist/compiled/react-is/index.js");if(!e(G))throw Error(`The default export is not a React Component in page: "${s}"`);if(!e(H))throw Error('The default export is not a React Component in page: "/_app"');if(!e(Z))throw Error('The default export is not a React Component in page: "/_document"');if((ev||V)&&(i={...i.amp?{amp:i.amp}:{}},eP=`${s}${n.url.endsWith("/")&&"/"!==s&&!eh?"/":""}`,n.url=s),"/404"===s&&(ed||C))throw Error(`\`pages/404\` ${k.Ei}`);if(M.Er.includes(s)&&(ed||C))throw Error(`\`pages${s}\` ${k.Ei}`)}for(let e of["getStaticProps","getServerSideProps","getStaticPaths"])if(null==G?void 0:G[e])throw Error(`page ${s} ${e} ${k.lk}`);await X.preloadAll(),(Q||C)&&!V&&A&&(g=!1!==(m=e(n,a,A)));let e_=!!(C||ed||!ei&&!Q||F),eE=new e4(s,i,eP,{isFallback:V},e_,N,l.locale,l.locales,l.defaultLocale,l.domainLocales,g,ep(n,"isLocaleDomain")),eR={back(){eE.back()},forward(){eE.forward()},refresh(){eE.reload()},push(e,t){let{scroll:r}=void 0===t?{}:t;eE.push(e,void 0,{scroll:r})},replace(e,t){let{scroll:r}=void 0===t?{}:t;eE.replace(e,void 0,{scroll:r})},prefetch(e){eE.prefetch(e)}},eC={},eT=(0,I.createStyleRegistry)(),ej={ampFirst:!0===w.amp,hasQuery:!!i.amp,hybrid:"hybrid"===w.amp},e$=function(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}(ej),eA=function(e){void 0===e&&(e=!1);let t=[L().createElement("meta",{charSet:"utf-8"})];return e||t.push(L().createElement("meta",{name:"viewport",content:"width=device-width"})),t}(e$),eL=[],eN={};ec&&(eN.beforeInteractive=[].concat(ec()).filter(e=>"beforeInteractive"===e.props.strategy).map(e=>e.props));let eO=({children:e})=>{var t;return L().createElement(eV.Provider,{value:eR},L().createElement(eH.Provider,{value:eE.isReady&&eE.query?(t=eE.asPath,new URL(t,"http://n").searchParams):new URLSearchParams},L().createElement(eG,{router:eE,isAutoExport:ev},L().createElement(eB.Provider,{value:function(e){if(!e.isReady||!e.query)return null;let t={},r=function(e){let{parameterizedRoute:t,groups:r}=function(e){let t=eS(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=et.find(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&o){let{key:e,optional:a,repeat:s}=eZ(o[1]);return r[e]={pos:n++,repeat:s,optional:a},"/"+eJ(t)+"([^/]+?)"}if(!o)return"/"+eJ(e);{let{key:e,repeat:t,optional:a}=eZ(o[1]);return r[e]={pos:n++,repeat:t,optional:a},t?a?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}(e.pathname),n=Object.keys(r.groups);for(let r of n)t[r]=e.query[r];return t}(eE)},L().createElement(K.Provider,{value:eE},L().createElement(B.Provider,{value:ej},L().createElement(U.Provider,{value:{updateHead:e=>{eA=e},updateScripts:e=>{eC=e},scripts:eN,mountedInstances:new Set}},L().createElement(W.Provider,{value:e=>eL.push(e)},L().createElement(I.StyleRegistry,{registry:eT},L().createElement(eM.Provider,{value:D},e))))))))))},eI=()=>null,eD=({children:e})=>L().createElement(L().Fragment,null,L().createElement(eI,null),L().createElement(eO,null,L().createElement(L().Fragment,null,b?L().createElement(L().Fragment,null,e,L().createElement(eI,null)):e,L().createElement(eI,null)))),ez={err:y,req:ev?void 0:n,res:ev?void 0:a,pathname:s,query:i,asPath:eP,locale:l.locale,locales:l.locales,defaultLocale:l.defaultLocale,AppTree:e=>L().createElement(eD,null,e3(H,G,{...e,router:eE})),defaultGetInitialProps:async(e,t={})=>{let{html:r,head:n}=await e.renderPage({enhanceApp:e=>t=>L().createElement(e,t)}),o=eT.styles({nonce:t.nonce});return eT.flush(),{html:r,head:n,styles:o}}},eU=!Q&&(l.nextExport||b&&(ev||V)),eW=()=>{let e=eT.styles();return eT.flush(),L().createElement(L().Fragment,null,e)};if(f=await es(H,{AppTree:ez.AppTree,Component:G,router:eE,ctx:ez}),(Q||C)&&g&&(f.__N_PREVIEW=!0),Q&&(f[M.NO]=!0),Q&&!V){let e,t;try{e=await (0,ef.getTracer)().trace(c.getStaticProps,{spanName:`getStaticProps ${s}`,attributes:{"next.route":s}},()=>E({...eh?{params:i}:void 0,...g?{draftMode:!0,preview:!0,previewData:m}:void 0,locales:l.locales,locale:l.locale,defaultLocale:l.defaultLocale}))}catch(e){throw e&&"ENOENT"===e.code&&delete e.code,e}if(null==e)throw Error(k.q6);let r=Object.keys(e).filter(e=>"revalidate"!==e&&"props"!==e&&"redirect"!==e&&"notFound"!==e);if(r.includes("unstable_revalidate"))throw Error(k.Eo);if(r.length)throw Error(e2("getStaticProps",r));if("notFound"in e&&e.notFound){if("/404"===s)throw Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');v.isNotFound=!0}if("redirect"in e&&e.redirect&&"object"==typeof e.redirect){if(e8(e.redirect,n,"getStaticProps"),er)throw Error(`\`redirect\` can not be returned from getStaticProps during prerendering (${n.url})
See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`);e.props={__N_REDIRECT:e.redirect.destination,__N_REDIRECT_STATUS:em(e.redirect)},void 0!==e.redirect.basePath&&(e.props.__N_REDIRECT_BASE_PATH=e.redirect.basePath),v.isRedirect=!0}if((b||er)&&!v.isNotFound&&!z(s,"getStaticProps",e.props))throw Error("invariant: getStaticProps did not return valid props. Please report this.");if("revalidate"in e){if(e.revalidate&&"export"===l.nextConfigOutput)throw Error('ISR cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if("number"==typeof e.revalidate){if(Number.isInteger(e.revalidate)){if(e.revalidate<=0)throw Error(`A page's revalidate option can not be less than or equal to zero for ${n.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.

To never revalidate, you can set revalidate to \`false\` (only ran once at build-time).
To revalidate as soon as possible, you can set the value to \`1\`.`);e.revalidate>31536e3&&console.warn(`Warning: A page's revalidate option was set to more than a year for ${n.url}. This may have been done in error.
To only run getStaticProps at build-time and not revalidate at runtime, you can set \`revalidate\` to \`false\`!`),t=e.revalidate}else throw Error(`A page's revalidate option must be seconds expressed as a natural number for ${n.url}. Mixed numbers, such as '${e.revalidate}', cannot be used.
Try changing the value to '${Math.ceil(e.revalidate)}' or using \`Math.ceil()\` if you're computing the value.`)}else if(!0===e.revalidate)t=1;else if(!1===e.revalidate||void 0===e.revalidate)t=!1;else throw Error(`A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(e.revalidate)}' for ${n.url}`)}else t=!1;if(f.pageProps=Object.assign({},f.pageProps,"props"in e?e.props:void 0),v.revalidate=t,v.pageData=f,v.isNotFound)return new ek(null,{metadata:v})}if(C&&(f[M.uY]=!0),C&&!V){let e;let t=!1;try{e=await (0,ef.getTracer)().trace(c.getServerSideProps,{spanName:`getServerSideProps ${s}`,attributes:{"next.route":s}},async()=>C({req:n,res:a,query:i,resolvedUrl:l.resolvedUrl,...eh?{params:j}:void 0,...!1!==m?{draftMode:!0,preview:!0,previewData:m}:void 0,locales:l.locales,locale:l.locale,defaultLocale:l.defaultLocale}))}catch(e){throw"object"==typeof e&&null!==e&&"name"in e&&"message"in e&&"ENOENT"===e.code&&delete e.code,e}if(null==e)throw Error(k.Lx);e.props instanceof Promise&&(t=!0);let r=Object.keys(e).filter(e=>"props"!==e&&"redirect"!==e&&"notFound"!==e);if(e.unstable_notFound)throw Error(`unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${s}`);if(e.unstable_redirect)throw Error(`unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${s}`);if(r.length)throw Error(e2("getServerSideProps",r));if("notFound"in e&&e.notFound){if("/404"===s)throw Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');return v.isNotFound=!0,new ek(null,{metadata:v})}if("redirect"in e&&"object"==typeof e.redirect&&(e8(e.redirect,n,"getServerSideProps"),e.props={__N_REDIRECT:e.redirect.destination,__N_REDIRECT_STATUS:em(e.redirect)},void 0!==e.redirect.basePath&&(e.props.__N_REDIRECT_BASE_PATH=e.redirect.basePath),v.isRedirect=!0),t&&(e.props=await e.props),(b||er)&&!z(s,"getServerSideProps",e.props))throw Error("invariant: getServerSideProps did not return valid props. Please report this.");f.pageProps=Object.assign({},f.pageProps,e.props),v.pageData=f}if(T&&!Q||v.isRedirect)return new ek(JSON.stringify(f),{metadata:v});if(V&&(f.pageProps={}),ea(a)&&!Q)return new ek(null,{metadata:v});let eY=S;if(ev&&eh){let e;let t=(e=(function(e){let t=/^\/index(\/|$)/.test(e)&&!en(e)?"/index"+e:"/"===e?"/index":ee(e);{let{posix:e}=r("path"),n=e.normalize(t);if(n!==t)throw new el("Requested and resolved page mismatch: "+t+" "+n)}return t})(s).replace(/\\/g,"/")).startsWith("/index/")&&!en(e)?e.slice(6):"/index"!==e?e:"/";t in eY.pages&&(eY={...eY,pages:{...eY.pages,[t]:[...eY.pages[t],...eY.lowPriorityFiles.filter(e=>e.includes("_buildManifest"))]},lowPriorityFiles:eY.lowPriorityFiles.filter(e=>!e.includes("_buildManifest"))})}let eQ=({children:e})=>e$?e:L().createElement("div",{id:"__next"},e),eX=async()=>{let e,t,r;async function n(e){let t=async(t={})=>{if(ez.err&&_){e&&e(H,G);let t=await e1(L().createElement(eQ,null,L().createElement(_,{error:ez.err})));return{html:t,head:eA}}if(b&&(f.router||f.Component))throw Error("'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props");let{App:r,Component:n}="function"==typeof t?{App:H,Component:t(G)}:{App:t.enhanceApp?t.enhanceApp(H):H,Component:t.enhanceComponent?t.enhanceComponent(G):G};if(e)return e(r,n).then(async e=>{await e.allReady;let t=await ex(e);return{html:t,head:eA}});let o=await e1(L().createElement(eQ,null,L().createElement(eD,null,e3(r,n,{...f,router:eE}))));return{html:o,head:eA}},r={...ez,renderPage:t},n=await es(Z,r);if(ea(a)&&!Q)return null;if(!n||"string"!=typeof n.html){let e=`"${eo(Z)}.getInitialProps()" should resolve to an object with a "html" prop set with a valid html string`;throw Error(e)}return{docProps:n,documentCtx:r}}Z[M.wU];let o=(e,t)=>{let r=e||H,n=t||G;return ez.err&&_?L().createElement(eQ,null,L().createElement(_,{error:ez.err})):L().createElement(eQ,null,L().createElement(eD,null,e3(r,n,{...f,router:eE})))},s=async(e,t)=>{let r=o(e,t);return await function({ReactDOMServer:e,element:t,streamOptions:r}){return(0,ef.getTracer)().trace(p.renderToReadableStream,async()=>e.renderToReadableStream(t,r))}({ReactDOMServer:O(),element:r})},i=(0,ef.getTracer)().wrap(c.createBodyResult,(e,t)=>ew(e,{suffix:t,inlinedDataStream:void 0,isStaticGeneration:!0,getServerInsertedHTML:()=>e1(eW()),serverInsertedHTMLToHead:!1,validateRootLayout:void 0})),l=!!Z.getInitialProps;if(l){if(null===(t=await n(s)))return null;let{docProps:r}=t;e=e=>i(eb(r.html+e))}else{let r=await s(H,G);e=e=>i(r,e),t={}}let{docProps:u}=t||{};return l?(r=u.styles,eA=u.head):(r=eT.styles(),eT.flush()),{bodyResult:e,documentElement:e=>L().createElement(Z,{...e,...u}),head:eA,headTags:[],styles:r}};null==(d=(0,ef.getTracer)().getRootSpanAttributes())||d.set("next.route",l.page);let e0=await (0,ef.getTracer)().trace(c.renderDocument,{spanName:`render route (pages) ${l.page}`,attributes:{"next.route":l.page}},async()=>eX());if(!e0)return new ek(null,{metadata:v});let e9=new Set,e6=new Set;for(let e of eL){let t=P[e];t&&(e9.add(t.id),t.files.forEach(e=>{e6.add(e)}))}let e5=ej.hybrid,{assetPrefix:e7,buildId:te,customServer:tt,defaultLocale:tr,disableOptimizedLoading:tn,domainLocales:to,locale:ta,locales:ts,runtimeConfig:ti}=l,tl={__NEXT_DATA__:{props:f,page:s,query:i,buildId:te,assetPrefix:""===e7?void 0:e7,runtimeConfig:ti,nextExport:!0===eU||void 0,autoExport:!0===ev||void 0,isFallback:V,isExperimentalCompile:F,dynamicIds:0===e9.size?void 0:Array.from(e9),err:l.err?function(e,t){if(e){let e;return e="server",e=r("next/dist/compiled/@next/react-dev-overlay/dist/middleware").getErrorSource(t)||"server",{name:t.name,source:e,message:eq()(t.message),stack:t.stack,digest:t.digest}}return{name:"Internal Server Error.",message:"500 - Internal Server Error.",statusCode:500}}(b,l.err):void 0,gsp:!!E||void 0,gssp:!!C||void 0,customServer:tt,gip:!!ed||void 0,appGip:!ei||void 0,locale:ta,locales:ts,defaultLocale:tr,domainLocales:to,isPreview:!0===g||void 0,notFoundSrcPage:Y&&b?Y:void 0},strictNextHead:l.strictNextHead,buildManifest:eY,docComponentsRendered:{},dangerousAsPath:eE.asPath,canonicalBase:!l.ampPath&&ep(n,"didStripLocale")?`${l.canonicalBase||""}/${l.locale}`:l.canonicalBase,ampPath:x,inAmpMode:e$,isDevelopment:!!b,hybridAmp:e5,dynamicImports:Array.from(e6),assetPrefix:e7,unstable_runtimeJS:w.unstable_runtimeJS,unstable_JsPreload:w.unstable_JsPreload,assetQueryString:J,scriptLoader:eC,locale:ta,disableOptimizedLoading:tn,head:e0.head,headTags:e0.headTags,styles:e0.styles,crossOrigin:l.crossOrigin,optimizeCss:l.optimizeCss,optimizeFonts:l.optimizeFonts,nextConfigOutput:l.nextConfigOutput,nextScriptWorkers:l.nextScriptWorkers,runtime:q,largePageDataBytes:l.largePageDataBytes,nextFontManifest:l.nextFontManifest},tu=L().createElement(B.Provider,{value:ej},L().createElement(eu.Provider,{value:tl},e0.documentElement(tl))),td=await (0,ef.getTracer)().trace(c.renderToString,async()=>e1(tu)),[tc,tp]=td.split("<next-js-internal-body-render-target></next-js-internal-body-render-target>",2),th="";td.startsWith(eK)||(th+=eK),th+=tc,e$&&(th+="<!-- __NEXT_DATA__ -->");let tm=await ex(ey(eb(th),await e0.bodyResult(tp))),tf=await o(s,tm,l,{inAmpMode:e$,hybridAmp:e5});return new ek(tf,{metadata:v})}let e6=(e,t,r,n,o)=>e9(e,t,r,n,o,o),e5=L().createContext(null);function e7(e){let t=(0,A.useContext)(e5);t&&t(e)}class te extends j{constructor(e){super(e),this.components=e.components}render(e,t,r){return e9(e,t,r.page,r.query,r.renderOpts,{App:this.components.App,Document:this.components.Document})}}let tt={contexts:T},tr=te})(),module.exports=n})();
//# sourceMappingURL=pages.runtime.prod.js.map