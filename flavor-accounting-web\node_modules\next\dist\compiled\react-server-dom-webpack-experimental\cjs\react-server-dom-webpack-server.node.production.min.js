/*
 React
 react-server-dom-webpack-server.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("util");require("crypto");var ba=require("async_hooks"),ca=require("react"),da=require("react-dom"),l=null,m=0,p=!0;function q(a,b){a=a.write(b);p=p&&a}
function r(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<m&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),q(a,ea.encode(b));else{var d=l;0<m&&(d=l.subarray(m));d=ea.encodeInto(b,d);var c=d.read;m+=d.written;c<b.length&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=ea.encodeInto(b.slice(c),l).written);2048===m&&(q(a,l),l=new Uint8Array(2048),m=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<m&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),q(a,b)):(d=l.length-m,d<b.byteLength&&
(0===d?q(a,l):(l.set(b.subarray(0,d),m),m+=d,q(a,l),b=b.subarray(d)),l=new Uint8Array(2048),m=0),l.set(b,m),m+=b.byteLength,2048===m&&(q(a,l),l=new Uint8Array(2048),m=0)));return p}var ea=new aa.TextEncoder,t=Symbol.for("react.client.reference"),v=Symbol.for("react.server.reference");function x(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:b},$$async:{value:d}})}var fa=Function.prototype.bind,ha=Array.prototype.slice;
function ia(){var a=fa.apply(this,arguments);if(this.$$typeof===v){var b=ha.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:v},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:ia}})}return a}
var ja=Promise.prototype,ka={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function la(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "__esModule":var d=a.$$id;a.default=x(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=x({},a.$$id,!0),e=new Proxy(c,ma);a.status="fulfilled";a.value=e;return a.then=x(function(f){return Promise.resolve(f(e))},a.$$id+"#then",!1)}c=a[b];c||(c=x(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ka));return c}
var ma={get:function(a,b){return la(a,b)},getOwnPropertyDescriptor:function(a,b){var d=Object.getOwnPropertyDescriptor(a,b);d||(d={value:la(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,d));return d},getPrototypeOf:function(){return ja},set:function(){throw Error("Cannot assign to a client module from a server module.");}},ua={prefetchDNS:na,preconnect:oa,preload:pa,preloadModule:qa,preinitStyle:ra,preinitScript:sa,preinitModuleScript:ta};
function na(a){if("string"===typeof a&&a){var b=y();if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),z(b,"D",a))}}}function oa(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?z(d,"C",[a,b]):z(d,"C",a))}}}
function pa(a,b,d){if("string"===typeof a){var c=y();if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,h=d.imageSizes,k="";"string"===typeof g&&""!==g?(k+="["+g+"]","string"===typeof h&&(k+="["+h+"]")):k+="[][]"+a;f+="[image]"+k}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=A(d))?z(c,"L",[a,b,d]):z(c,"L",[a,b]))}}}function qa(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=A(b))?z(d,"m",[a,b]):z(d,"m",a)}}}
function ra(a,b,d){if("string"===typeof a){var c=y();if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=A(d))?z(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?z(c,"S",[a,b]):z(c,"S",a)}}}function sa(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=A(b))?z(d,"X",[a,b]):z(d,"X",a)}}}function ta(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=A(b))?z(d,"M",[a,b]):z(d,"M",a)}}}
function A(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var va=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,wa=new ba.AsyncLocalStorage,B=Symbol.for("react.element"),xa=Symbol.for("react.fragment"),ya=Symbol.for("react.provider"),za=Symbol.for("react.server_context"),Aa=Symbol.for("react.forward_ref"),Ba=Symbol.for("react.suspense"),Ca=Symbol.for("react.suspense_list"),Da=Symbol.for("react.memo"),C=Symbol.for("react.lazy"),Ea=Symbol.for("react.default_value"),Fa=Symbol.for("react.memo_cache_sentinel"),Ga=Symbol.for("react.postpone"),
Ha=Symbol.iterator,D=null;function Ia(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Ia(a,d);b.context._currentValue=b.value}}}function Ja(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ja(a)}
function Ka(a){var b=a.parent;null!==b&&Ka(b);a.context._currentValue=a.value}function La(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Ia(a,b):La(a,b)}
function Ma(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?Ia(a,d):Ma(a,d);b.context._currentValue=b.value}function Na(a){var b=D;b!==a&&(null===b?Ka(a):null===a?Ja(b):b.depth===a.depth?Ia(b,a):b.depth>a.depth?La(b,a):Ma(b,a),D=a)}function Oa(a,b){var d=a._currentValue;a._currentValue=b;var c=D;return D=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var Pa=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Qa(){}function Ra(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Qa,Qa),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Sa=b;throw Pa;}}var Sa=null;
function Ta(){if(null===Sa)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Sa;Sa=null;return a}var E=null,Ua=0,F=null;function Va(){var a=F;F=null;return a}function Wa(a){return a._currentValue}
var $a={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:G,useTransition:G,readContext:Wa,useContext:Wa,useReducer:G,useRef:G,useState:G,useInsertionEffect:G,useLayoutEffect:G,useImperativeHandle:G,useEffect:G,useId:Xa,useSyncExternalStore:G,useCacheRefresh:function(){return Ya},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Fa;return b},use:Za};
function G(){throw Error("This Hook is not supported in Server Components.");}function Ya(){throw Error("Refreshing the cache is not supported in Server Components.");}function Xa(){if(null===E)throw Error("useId can only be used while React is rendering");var a=E.identifierCount++;return":"+E.identifierPrefix+"S"+a.toString(32)+":"}
function Za(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Ua;Ua+=1;null===F&&(F=[]);return Ra(F,a,b)}if(a.$$typeof===za)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function ab(){return(new AbortController).signal}function bb(){var a=y();return a?a.cache:new Map}
var cb={getCacheSignal:function(){var a=bb(),b=a.get(ab);void 0===b&&(b=ab(),a.set(ab,b));return b},getCacheForType:function(a){var b=bb(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},db=Array.isArray,eb=Object.getPrototypeOf;function fb(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function gb(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(db(a))return"[...]";a=fb(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function hb(a){if("string"===typeof a)return a;switch(a){case Ba:return"Suspense";case Ca:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case Aa:return hb(a.render);case Da:return hb(a.type);case C:var b=a._payload;a=a._init;try{return hb(a(b))}catch(d){}}return""}
function I(a,b){var d=fb(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(db(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?I(g):gb(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===B)e="<"+hb(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var h=f[g],k=JSON.stringify(h);e+=('"'+h+'"'===k?h:k)+": ";k=a[h];k="object"===typeof k&&null!==k?I(k):
gb(k);h===b?(d=e.length,c=k.length,e+=k):e=10>k.length&&40>e.length+k.length?e+k:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var ib=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,jb=ib.ContextRegistry,J=ca.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!J)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var kb=Object.prototype,K=JSON.stringify,lb=J.TaintRegistryObjects,L=J.TaintRegistryValues,mb=J.TaintRegistryByteLengths,nb=J.TaintRegistryPendingRequests,ob=J.ReactCurrentCache,pb=ib.ReactCurrentDispatcher;function M(a){throw Error(a);}
function qb(a){a=a.taintCleanupQueue;nb.delete(a);for(var b=0;b<a.length;b++){var d=a[b],c=L.get(d);void 0!==c&&(1===c.count?L.delete(d):c.count--)}a.length=0}function rb(a){console.error(a)}function sb(){}
function tb(a,b,d,c,e,f){if(null!==ob.current&&ob.current!==cb)throw Error("Currently React only supports one RSC renderer at a time.");va.current=ua;ob.current=cb;var g=new Set,h=[],k=[];nb.add(k);var n=new Set,w={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:n,abortableTasks:g,pingedTasks:h,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:k,onError:void 0===d?rb:d,onPostpone:void 0===f?sb:f,toJSON:function(u,H){return ub(w,this,u,H)}};w.pendingChunks++;b=vb(c);a=wb(w,a,b,g);h.push(a);return w}var N=null;function y(){if(N)return N;var a=wa.getStore();return a?a:null}var xb={};
function yb(a,b){a.pendingChunks++;var d=wb(a,null,D,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,zb(a,d),d.id;case "rejected":var c=b.reason;"object"===typeof c&&null!==c&&c.$$typeof===Ga?(Ab(a,c.message),Bb(a,d.id)):(c=O(a,c),P(a,d.id,c));return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=
e;zb(a,d)},function(e){d.status=4;a.abortableTasks.delete(d);e=O(a,e);P(a,d.id,e);null!==a.destination&&Q(a,a.destination)});return d.id}function z(a,b,d){d=K(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;a.completedHintChunks.push(b+d+"\n");Cb(a)}function Db(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function Eb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:C,_payload:a,_init:Db}}
function R(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===t)return[B,b,d,e];Ua=0;F=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:Eb(e):e}if("string"===typeof b)return[B,b,d,e];if("symbol"===typeof b)return b===xa?e.children:[B,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===t)return[B,b,d,e];switch(b.$$typeof){case C:var g=
b._init;b=g(b._payload);return R(a,b,d,c,e,f);case Aa:return a=b.render,Ua=0,F=f,a(e,void 0);case Da:return R(a,b.type,d,c,e,f);case ya:return Oa(b._context,e.value),[B,b,d,{value:e.value,children:e.children,__pop:xb}]}}throw Error("Unsupported Server Component type: "+gb(b));}function zb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return Fb(a)}))}
function wb(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return zb(a,e)},thenableState:null};c.add(e);return e}function S(a){return"$"+a.toString(16)}function Gb(a,b,d){a=K(d);return b.toString(16)+":"+a+"\n"}
function Hb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===B&&"1"===d?"$L"+g.toString(16):S(g);try{var h=a.bundlerConfig,k=c.$$id;g="";var n=h[k];if(n)g=n.name;else{var w=k.lastIndexOf("#");-1!==w&&(g=k.slice(w+1),n=h[k.slice(0,w)]);if(!n)throw Error('Could not find the module "'+k+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var u=!0===c.$$async?[n.id,n.chunks,g,1]:[n.id,n.chunks,
g];a.pendingChunks++;var H=a.nextChunkId++,dc=K(u),ec=H.toString(16)+":I"+dc+"\n";a.completedImportChunks.push(ec);f.set(e,H);return b[0]===B&&"1"===d?"$L"+H.toString(16):S(H)}catch(fc){return a.pendingChunks++,b=a.nextChunkId++,d=O(a,fc),P(a,b,d),S(b)}}function T(a,b){a.pendingChunks++;b=wb(a,b,D,a.abortableTasks);Ib(a,b);return b.id}
function U(a,b,d){if(mb.has(d.byteLength)){var c=L.get(String.fromCharCode.apply(String,new Uint8Array(d.buffer,d.byteOffset,d.byteLength)));void 0!==c&&M(c.message)}a.pendingChunks+=2;c=a.nextChunkId++;d=new Uint8Array(d.buffer,d.byteOffset,d.byteLength);var e=d.byteLength;b=c.toString(16)+":"+b+e.toString(16)+",";a.completedRegularChunks.push(b,d);return S(c)}var V=!1;
function ub(a,b,d,c){switch(c){case B:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===B||c.$$typeof===C);)try{switch(c.$$typeof){case B:var e=a.writtenObjects,f=e.get(c);if(void 0!==f){if(-1===f){var g=T(a,c);return S(g)}if(V===c)V=null;else return S(f)}else e.set(c,-1);var h=c;c=R(a,h.type,h.key,h.ref,h.props,null);break;case C:var k=c._init;c=k(c._payload)}}catch(n){d=n===Pa?Ta():n;if("object"===typeof d&&null!==d){if("function"===typeof d.then)return a.pendingChunks++,a=wb(a,c,D,a.abortableTasks),
c=a.ping,d.then(c,c),a.thenableState=Va(),"$L"+a.id.toString(16);if(d.$$typeof===Ga)return c=d,a.pendingChunks++,d=a.nextChunkId++,Ab(a,c.message),Bb(a,d),"$L"+d.toString(16)}a.pendingChunks++;c=a.nextChunkId++;d=O(a,d);P(a,c,d);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){e=lb.get(c);void 0!==e&&M(e);if(c.$$typeof===t)return Hb(a,b,d,c);b=a.writtenObjects;e=b.get(c);if("function"===typeof c.then){if(void 0!==e)if(V===c)V=null;else return"$@"+e.toString(16);a=yb(a,c);
b.set(c,a);return"$@"+a.toString(16)}if(c.$$typeof===ya)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=Gb(a,d,"$P"+c),a.completedRegularChunks.push(c)),S(d);if(c===xb){a=D;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===Ea?a.context._defaultValue:c;D=a.parent;return}if(void 0!==e){if(-1===e)return a=T(a,c),S(a);if(V===c)V=null;
else return S(e)}else b.set(c,-1);if(db(c))return c;if(c instanceof Map){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d][0],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$Q"+T(a,c).toString(16)}if(c instanceof Set){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$W"+T(a,c).toString(16)}if(c instanceof ArrayBuffer)return U(a,"A",new Uint8Array(c));if(c instanceof Int8Array)return U(a,
"C",c);if(c instanceof Uint8Array)return U(a,"c",c);if(c instanceof Uint8ClampedArray)return U(a,"U",c);if(c instanceof Int16Array)return U(a,"S",c);if(c instanceof Uint16Array)return U(a,"s",c);if(c instanceof Int32Array)return U(a,"L",c);if(c instanceof Uint32Array)return U(a,"l",c);if(c instanceof Float32Array)return U(a,"F",c);if(c instanceof Float64Array)return U(a,"D",c);if(c instanceof BigInt64Array)return U(a,"N",c);if(c instanceof BigUint64Array)return U(a,"m",c);if(c instanceof DataView)return U(a,
"V",c);null===c||"object"!==typeof c?a=null:(a=Ha&&c[Ha]||c["@@iterator"],a="function"===typeof a?a:null);if(a)return Array.from(c);a=eb(c);if(a!==kb&&(null===a||null!==eb(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return c}if("string"===typeof c){e=L.get(c);void 0!==e&&M(e.message);if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=
2,d=a.nextChunkId++,b="string"===typeof c?Buffer.byteLength(c,"utf8"):c.byteLength,b=d.toString(16)+":T"+b.toString(16)+",",a.completedRegularChunks.push(b,c),S(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){e=lb.get(c);void 0!==e&&M(e);if(c.$$typeof===t)return Hb(a,b,
d,c);if(c.$$typeof===v)return d=a.writtenServerReferences,b=d.get(c),void 0!==b?a="$F"+b.toString(16):(b=c.$$bound,b={id:c.$$id,bound:b?Promise.resolve(b):null},a=T(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+I(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+
I(b,d));}if("symbol"===typeof c){e=a.writtenSymbols;f=e.get(c);if(void 0!==f)return S(f);f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+I(b,d));a.pendingChunks++;d=a.nextChunkId++;b=Gb(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return S(d)}if("bigint"===typeof c)return a=L.get(c),void 0!==a&&M(a.message),"$n"+c.toString(10);
throw Error("Type "+typeof c+" is not supported in Client Component props."+I(b,d));}function Ab(a,b){a=a.onPostpone;a(b)}function O(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}
function Jb(a,b){qb(a);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}function Bb(a,b){b=b.toString(16)+":P\n";a.completedErrorChunks.push(b)}function P(a,b,d){d={digest:d};b=b.toString(16)+":E"+K(d)+"\n";a.completedErrorChunks.push(b)}
function Ib(a,b){if(0===b.status){Na(b.context);try{var d=b.model;if("object"===typeof d&&null!==d&&d.$$typeof===B){a.writtenObjects.set(d,b.id);var c=d,e=b.thenableState;b.model=d;d=R(a,c.type,c.key,c.ref,c.props,e);for(b.thenableState=null;"object"===typeof d&&null!==d&&d.$$typeof===B;)a.writtenObjects.set(d,b.id),c=d,b.model=d,d=R(a,c.type,c.key,c.ref,c.props,null)}"object"===typeof d&&null!==d&&a.writtenObjects.set(d,b.id);var f=b.id;V=d;var g=K(d,a.toJSON),h=f.toString(16)+":"+g+"\n";a.completedRegularChunks.push(h);
a.abortableTasks.delete(b);b.status=1}catch(k){f=k===Pa?Ta():k;if("object"===typeof f&&null!==f){if("function"===typeof f.then){a=b.ping;f.then(a,a);b.thenableState=Va();return}if(f.$$typeof===Ga){a.abortableTasks.delete(b);b.status=4;Ab(a,f.message);Bb(a,b.id);return}}a.abortableTasks.delete(b);b.status=4;f=O(a,f);P(a,b.id,f)}}}
function Fb(a){var b=pb.current;pb.current=$a;var d=N;E=N=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++)Ib(a,c[e]);null!==a.destination&&Q(a,a.destination)}catch(f){O(a,f),Jb(a,f)}finally{pb.current=b,E=null,N=d}}
function Q(a,b){l=new Uint8Array(2048);m=0;p=!0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)if(a.pendingChunks--,!r(b,d[c])){a.destination=null;c++;break}d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)if(!r(b,e[c])){a.destination=null;c++;break}e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)if(a.pendingChunks--,!r(b,f[c])){a.destination=null;c++;break}f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)if(a.pendingChunks--,!r(b,g[c])){a.destination=
null;c++;break}g.splice(0,c)}finally{a.flushScheduled=!1,l&&0<m&&b.write(l.subarray(0,m)),l=null,m=0,p=!0}"function"===typeof b.flush&&b.flush();0===a.pendingChunks&&(qb(a),b.end())}function Kb(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return wa.run(a,Fb,a)})}function Cb(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setImmediate(function(){return Q(a,b)})}}
function Lb(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{Q(a,b)}catch(d){O(a,d),Jb(a,d)}}}
function Mb(a,b){try{var d=a.abortableTasks;if(0<d.size){a.pendingChunks++;var c=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===Ga)Ab(a,b.message),Bb(a,c,b);else{var e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=O(a,e);P(a,c,f,e)}d.forEach(function(g){g.status=3;var h=S(c);g=Gb(a,g.id,h);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&Q(a,a.destination)}catch(g){O(a,g),Jb(a,g)}}
function vb(a){if(a){var b=D;Na(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];if(!jb[e]){var f={$$typeof:za,_currentValue:Ea,_currentValue2:Ea,_defaultValue:Ea,_threadCount:0,Provider:null,Consumer:null,_globalName:e};f.Provider={$$typeof:ya,_context:f};jb[e]=f}Oa(jb[e],c)}a=D;Na(b);return a}return null}
function Nb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[c.id,c.chunks,d]}var Ob=new Map;
function Pb(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function Qb(){}
function Rb(a){for(var b=a[1],d=[],c=0;c<b.length;){var e=b[c++];b[c++];var f=Ob.get(e);if(void 0===f){f=__webpack_chunk_load__(e);d.push(f);var g=Ob.set.bind(Ob,e,null);f.then(g,Qb);Ob.set(e,f)}else null!==f&&d.push(f)}return 4===a.length?0===d.length?Pb(a[0]):Promise.all(d).then(function(){return Pb(a[0])}):0<d.length?Promise.all(d):null}
function W(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function Sb(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}Sb.prototype=Object.create(Promise.prototype);
Sb.prototype.then=function(a,b){switch(this.status){case "resolved_model":Tb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Ub(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function Vb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Ub(d,b)}}function Wb(a,b,d,c,e,f){var g=Nb(a._bundlerConfig,b);a=Rb(g);if(d)d=Promise.all([d,a]).then(function(h){h=h[0];var k=W(g);return k.bind.apply(k,[null].concat(h))});else if(a)d=Promise.resolve(a).then(function(){return W(g)});else return W(g);d.then(Xb(c,e,f),Yb(c));return null}var X=null,Y=null;
function Tb(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Zb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Vb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new Sb("resolved_model",c,null,a):new Sb("pending",null,null,a),d.set(b,c));return c}function Xb(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Ub(e,c.value))}}function Yb(a){return function(b){return Vb(a,b)}}
function $b(a,b){a=Z(a,b);"resolved_model"===a.status&&Tb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function ac(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=$b(a,c),Wb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=$b(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=$b(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,h){h.startsWith(e)&&f.append(h.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Tb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Xb(c,b,d),Yb(c)),null;default:throw a.reason;}}return c}
function bc(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?ac(e,this,f,g):g}};return e}
function cc(a,b,d){a._formData.append(b,d);var c=a._prefix;if(b.startsWith(c)&&(a=a._chunks,b=+b.slice(c.length),(b=a.get(b))&&"pending"===b.status&&(c=b.value,a=b.reason,b.status="resolved_model",b.value=d,null!==c)))switch(Tb(b),b.status){case "fulfilled":Ub(c,b.value);break;case "pending":case "blocked":b.value=c;b.reason=a;break;case "rejected":a&&Ub(a,b.reason)}}function gc(a){Zb(a,Error("Connection closed."))}
function hc(a,b,d){var c=Nb(a,b);a=Rb(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=W(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return W(c)}):Promise.resolve(W(c))}function ic(a,b,d){a=bc(b,d,a);gc(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}function jc(a,b){return function(){return Lb(b,a)}}function kc(a,b){return function(){a.destination=null;Mb(a,Error(b))}}
exports.createClientModuleProxy=function(a){a=x({},a,!1);return new Proxy(a,ma)};exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=ic(a,b,e),c=hc(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=hc(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};
exports.decodeFormState=function(a,b,d){var c=b.get("$ACTION_KEY");if("string"!==typeof c)return Promise.resolve(null);var e=null;b.forEach(function(g,h){h.startsWith("$ACTION_REF_")&&(g="$ACTION_"+h.slice(12)+":",e=ic(b,d,g))});if(null===e)return Promise.resolve(null);var f=e.id;return Promise.resolve(e.bound).then(function(g){return null===g?null:[a,c,f,g.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=bc(b,"",a);b=Z(a,0);gc(a);return b};
exports.decodeReplyFromBusboy=function(a,b){var d=bc(b,""),c=0,e=[];a.on("field",function(f,g){0<c?e.push(f,g):cc(d,f,g)});a.on("file",function(f,g,h){var k=h.filename,n=h.mimeType;if("base64"===h.encoding.toLowerCase())throw Error("React doesn't accept base64 encoded file uploads because we don't expect form data passed from a browser to ever encode data that way. If that's the wrong assumption, we can easily fix it.");c++;var w=[];g.on("data",function(u){w.push(u)});g.on("end",function(){var u=
new Blob(w,{type:n});d._formData.append(f,u,k);c--;if(0===c){for(u=0;u<e.length;u+=2)cc(d,e[u],e[u+1]);e.length=0}})});a.on("finish",function(){gc(d)});a.on("error",function(f){Zb(d,f)});return Z(d,0)};exports.registerClientReference=function(a,b,d){return x(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:v},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:ia}})};
exports.renderToPipeableStream=function(a,b,d){var c=tb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0),e=!1;Kb(c);return{pipe:function(f){if(e)throw Error("React currently only supports piping to one writable stream.");e=!0;Lb(c,f);f.on("drain",jc(f,c));f.on("error",kc(c,"The destination stream errored while writing data."));f.on("close",kc(c,"The destination stream closed early."));return f},abort:function(f){Mb(c,f)}}};

//# sourceMappingURL=react-server-dom-webpack-server.node.production.min.js.map
