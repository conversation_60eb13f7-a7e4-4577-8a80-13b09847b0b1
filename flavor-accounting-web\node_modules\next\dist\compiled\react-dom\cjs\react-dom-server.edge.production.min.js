/*
 React
 react-dom-server.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var aa=require("next/dist/compiled/react"),ca=require("react-dom");
function da(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&4294967295;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&4294967295;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&4294967295;return(e^e>>>16)>>>0}var k=null,m=0;
function p(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<m&&(a.enqueue(new Uint8Array(k.buffer,0,m)),k=new Uint8Array(512),m=0),a.enqueue(b);else{var c=k.length-m;c<b.byteLength&&(0===c?a.enqueue(k):(k.set(b.subarray(0,c),m),a.enqueue(k),b=b.subarray(c)),k=new Uint8Array(512),m=0);k.set(b,m);m+=b.byteLength}}function v(a,b){p(a,b);return!0}function ia(a){k&&0<m&&(a.enqueue(new Uint8Array(k.buffer,0,m)),k=null,m=0)}var ja=new TextEncoder;function x(a){return ja.encode(a)}
function z(a){return ja.encode(a)}function oa(a,b){"function"===typeof a.error?a.error(b):a.close()}
var A=Object.assign,C=Object.prototype.hasOwnProperty,pa=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),va={},wa={};
function xa(a){if(C.call(wa,a))return!0;if(C.call(va,a))return!1;if(pa.test(a))return wa[a]=!0;va[a]=!0;return!1}
var Ea=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Fa=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ga=/["'&<>]/;
function F(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ga.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Ha=/([A-Z])/g,Ia=/^ms-/,Ja=Array.isArray,Ka=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ra={pending:!1,data:null,method:null,action:null},Sa=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,$a={prefetchDNS:Ta,preconnect:Ua,preload:Va,preloadModule:Wa,preinitStyle:Xa,preinitScript:Ya,preinitModuleScript:Za},J=[],nb=z('"></template>'),ob=z("<script>"),pb=z("\x3c/script>"),qb=z('<script src="'),rb=z('<script type="module" src="'),sb=z('" nonce="'),tb=z('" integrity="'),
ub=z('" crossorigin="'),vb=z('" async="">\x3c/script>'),wb=/(<\/|<)(s)(cript)/gi;function xb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}var yb=z('<script type="importmap">'),Hb=z("\x3c/script>");
function Ib(a,b,c,d,e,f){var g=void 0===b?ob:z('<script nonce="'+F(b)+'">'),h=a.idPrefix,l=[],n=null,q=a.bootstrapScriptContent,t=a.bootstrapScripts,r=a.bootstrapModules;void 0!==q&&l.push(g,x((""+q).replace(wb,xb)),pb);void 0!==c&&("string"===typeof c?(n={src:c,chunks:[]},Jb(n.chunks,{src:c,async:!0,integrity:void 0,nonce:b})):(n={src:c.src,chunks:[]},Jb(n.chunks,{src:c.src,async:!0,integrity:c.integrity,nonce:b})));c=[];void 0!==d&&(c.push(yb),c.push(x((""+JSON.stringify(d)).replace(wb,xb))),c.push(Hb));
d=e?{preconnects:"",fontPreloads:"",highImagePreloads:"",remainingCapacity:"number"===typeof f?f:2E3}:null;e={placeholderPrefix:z(h+"P:"),segmentPrefix:z(h+"S:"),boundaryPrefix:z(h+"B:"),startInlineScript:g,htmlChunks:null,headChunks:null,externalRuntimeScript:n,bootstrapChunks:l,onHeaders:e,headers:d,resets:{font:{},dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],preconnectChunks:[],importMapChunks:c,preloadChunks:[],hoistableChunks:[],preconnects:new Set,
fontPreloads:new Set,highImagePreloads:new Set,styles:new Map,bootstrapScripts:new Set,scripts:new Set,bulkPreloads:new Set,preloads:{images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map},nonce:b,boundaryResources:null,stylesToHoist:!1};if(void 0!==t)for(g=0;g<t.length;g++)c=t[g],d=n=void 0,f={rel:"preload",as:"script",fetchPriority:"low",nonce:b},"string"===typeof c?f.href=h=c:(f.href=h=c.src,f.integrity=d="string"===typeof c.integrity?c.integrity:void 0,f.crossOrigin=n="string"===
typeof c||null==c.crossOrigin?void 0:"use-credentials"===c.crossOrigin?"use-credentials":""),c=a,q=h,c.scriptResources[q]=null,c.moduleScriptResources[q]=null,c=[],M(c,f),e.bootstrapScripts.add(c),l.push(qb,x(F(h))),b&&l.push(sb,x(F(b))),"string"===typeof d&&l.push(tb,x(F(d))),"string"===typeof n&&l.push(ub,x(F(n))),l.push(vb);if(void 0!==r)for(t=0;t<r.length;t++)f=r[t],n=h=void 0,d={rel:"modulepreload",fetchPriority:"low",nonce:b},"string"===typeof f?d.href=g=f:(d.href=g=f.src,d.integrity=n="string"===
typeof f.integrity?f.integrity:void 0,d.crossOrigin=h="string"===typeof f||null==f.crossOrigin?void 0:"use-credentials"===f.crossOrigin?"use-credentials":""),f=a,c=g,f.scriptResources[c]=null,f.moduleScriptResources[c]=null,f=[],M(f,d),e.bootstrapScripts.add(f),l.push(rb,x(F(g))),b&&l.push(sb,x(F(b))),"string"===typeof n&&l.push(tb,x(F(n))),"string"===typeof h&&l.push(ub,x(F(h))),l.push(vb);return e}
function Kb(a,b,c,d,e){var f=0;void 0!==b&&(f=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:f,bootstrapScriptContent:c,bootstrapScripts:d,bootstrapModules:e,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function N(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function Lb(a){return N("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function Mb(a,b,c){switch(b){case "noscript":return N(2,null,a.tagScope|1);case "select":return N(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return N(3,null,a.tagScope);case "picture":return N(2,null,a.tagScope|2);case "math":return N(4,null,a.tagScope);case "foreignObject":return N(2,null,a.tagScope);case "table":return N(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return N(6,null,a.tagScope);case "colgroup":return N(8,null,a.tagScope);case "tr":return N(7,null,a.tagScope)}return 5<=
a.insertionMode?N(2,null,a.tagScope):0===a.insertionMode?"html"===b?N(1,null,a.tagScope):N(2,null,a.tagScope):1===a.insertionMode?N(2,null,a.tagScope):a}var Nb=z("\x3c!-- --\x3e");function Ob(a,b,c,d){if(""===b)return d;d&&a.push(Nb);a.push(x(F(b)));return!0}var Pb=new Map,Qb=z(' style="'),Rb=z(":"),Sb=z(";");
function Tb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(C.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=x(F(d));e=x(F((""+e).trim()))}else f=Pb.get(d),void 0===f&&(f=z(F(d.replace(Ha,"-$1").toLowerCase().replace(Ia,"-ms-"))),Pb.set(d,f)),e="number"===typeof e?0===e||Ea.has(d)?x(""+
e):x(e+"px"):x(F((""+e).trim()));c?(c=!1,a.push(Qb,f,Rb,e)):a.push(Sb,f,Rb,e)}}c||a.push(O)}var Q=z(" "),Ub=z('="'),O=z('"'),Vb=z('=""');function Wb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,x(b),Vb)}function R(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(Q,x(b),Ub,x(F(c)),O)}function Xb(a){var b=a.nextFormID++;return a.idPrefix+b}var Yb=z(F("javascript:throw new Error('A React form was unexpectedly submitted.')")),Zb=z('<input type="hidden"');
function $b(a,b){this.push(Zb);if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");R(this,"name",b);R(this,"value",a);this.push(ac)}
function bc(a,b,c,d,e,f,g,h){var l=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Xb(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,l=b.data):(a.push(Q,x("formAction"),Ub,Yb,O),g=f=e=d=h=null,cc(b,c)));null!=h&&T(a,"name",h);null!=d&&T(a,"formAction",d);null!=e&&T(a,"formEncType",e);null!=f&&T(a,"formMethod",f);null!=g&&T(a,"formTarget",g);return l}
function T(a,b,c){switch(b){case "className":R(a,"class",c);break;case "tabIndex":R(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":R(a,b,c);break;case "style":Tb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;c=""+c;a.push(Q,x(b),Ub,x(F(c)),O);break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Wb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;c=""+c;a.push(Q,x("xlink:href"),Ub,x(F(c)),O);break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,x(b),Ub,x(F(c)),O);break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,x(b),Vb);break;case "capture":case "download":!0===c?a.push(Q,x(b),Vb):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,x(b),Ub,x(F(c)),O);break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(Q,x(b),Ub,x(F(c)),O);break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(Q,x(b),Ub,x(F(c)),O);break;case "xlinkActuate":R(a,"xlink:actuate",
c);break;case "xlinkArcrole":R(a,"xlink:arcrole",c);break;case "xlinkRole":R(a,"xlink:role",c);break;case "xlinkShow":R(a,"xlink:show",c);break;case "xlinkTitle":R(a,"xlink:title",c);break;case "xlinkType":R(a,"xlink:type",c);break;case "xmlBase":R(a,"xml:base",c);break;case "xmlLang":R(a,"xml:lang",c);break;case "xmlSpace":R(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Fa.get(b)||b,xa(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(Q,x(b),Ub,x(F(c)),O)}}}var U=z(">"),ac=z("/>");
function dc(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(x(""+b))}}function ec(a){var b="";aa.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}var fc=z(' selected=""'),gc=z('addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});');
function cc(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,gc,pb))}var hc=z("\x3c!--F!--\x3e"),ic=z("\x3c!--F--\x3e");
function jc(a,b,c,d,e,f,g){var h=b.rel,l=b.href,n=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof l||""===l)return M(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof n||null!=b.disabled||b.onLoad||b.onError)return M(a,b);f=d.styles.get(n);g=c.styleResources.hasOwnProperty(l)?c.styleResources[l]:void 0;null!==g?(c.styleResources[l]=null,f||(f={precedence:x(F(n)),rules:[],hrefs:[],sheets:new Map},d.styles.set(n,f)),b={state:0,props:A({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&kc(b.props,g),(c=d.preloads.stylesheets.get(l))&&0<c.length?c.length=0:b.state=1),f.sheets.set(l,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(l=f.sheets.get(l))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(l);e&&a.push(Nb);return null}if(b.onLoad||b.onError)return M(a,b);e&&a.push(Nb);switch(b.rel){case "preconnect":case "dns-prefetch":return M(d.preconnectChunks,b);case "preload":return M(d.preloadChunks,b);default:return M(d.hoistableChunks,
b)}}function M(a,b){a.push(V("link"));for(var c in b)if(C.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:T(a,c,d)}}a.push(ac);return null}
function uc(a,b,c){a.push(V(c));for(var d in b)if(C.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:T(a,d,e)}}a.push(ac);return null}
function vc(a,b){a.push(V("title"));var c=null,d=null,e;for(e in b)if(C.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:T(a,e,f)}}a.push(U);b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(x(F(""+b)));dc(a,d,c);a.push(wc("title"));return null}
function Jb(a,b){a.push(V("script"));var c=null,d=null,e;for(e in b)if(C.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:T(a,e,f)}}a.push(U);dc(a,d,c);"string"===typeof c&&a.push(x(F(c)));a.push(wc("script"));return null}
function xc(a,b,c){a.push(V(c));var d=c=null,e;for(e in b)if(C.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:T(a,e,f)}}a.push(U);dc(a,d,c);return"string"===typeof c?(a.push(x(F(c))),null):c}var yc=z("\n"),zc=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Ac=new Map;function V(a){var b=Ac.get(a);if(void 0===b){if(!zc.test(a))throw Error("Invalid tag: "+a);b=z("<"+a);Ac.set(a,b)}return b}var Bc=z("<!DOCTYPE html>");
function Cc(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(V("select"));var h=null,l=null,n;for(n in c)if(C.call(c,n)){var q=c[n];if(null!=q)switch(n){case "children":h=q;break;case "dangerouslySetInnerHTML":l=q;break;case "defaultValue":case "value":break;default:T(a,n,q)}}a.push(U);dc(a,l,h);return h;case "option":var t=f.selectedValue;a.push(V("option"));var r=null,w=null,y=null,K=null,u;for(u in c)if(C.call(c,
u)){var B=c[u];if(null!=B)switch(u){case "children":r=B;break;case "selected":y=B;break;case "dangerouslySetInnerHTML":K=B;break;case "value":w=B;default:T(a,u,B)}}if(null!=t){var G=null!==w?""+w:ec(r);if(Ja(t))for(var ka=0;ka<t.length;ka++){if(""+t[ka]===G){a.push(fc);break}}else""+t===G&&a.push(fc)}else y&&a.push(fc);a.push(U);dc(a,K,r);return r;case "textarea":a.push(V("textarea"));var D=null,W=null,E=null,ea;for(ea in c)if(C.call(c,ea)){var S=c[ea];if(null!=S)switch(ea){case "children":E=S;break;
case "value":D=S;break;case "defaultValue":W=S;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:T(a,ea,S)}}null===D&&null!==W&&(D=W);a.push(U);if(null!=E){if(null!=D)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ja(E)){if(1<E.length)throw Error("<textarea> can only have at most one child.");D=""+E[0]}D=""+E}"string"===typeof D&&"\n"===D[0]&&a.push(yc);null!==D&&a.push(x(F(""+D)));return null;
case "input":a.push(V("input"));var la=null,P=null,H=null,ma=null,ya=null,qa=null,ra=null,sa=null,La=null,fa;for(fa in c)if(C.call(c,fa)){var ba=c[fa];if(null!=ba)switch(fa){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":la=ba;break;case "formAction":P=ba;break;case "formEncType":H=ba;break;case "formMethod":ma=ba;break;case "formTarget":ya=ba;break;case "defaultChecked":La=ba;
break;case "defaultValue":ra=ba;break;case "checked":sa=ba;break;case "value":qa=ba;break;default:T(a,fa,ba)}}var rd=bc(a,d,e,P,H,ma,ya,la);null!==sa?Wb(a,"checked",sa):null!==La&&Wb(a,"checked",La);null!==qa?T(a,"value",qa):null!==ra&&T(a,"value",ra);a.push(ac);null!==rd&&rd.forEach($b,a);return null;case "button":a.push(V("button"));var ab=null,sd=null,td=null,ud=null,vd=null,wd=null,xd=null,bb;for(bb in c)if(C.call(c,bb)){var na=c[bb];if(null!=na)switch(bb){case "children":ab=na;break;case "dangerouslySetInnerHTML":sd=
na;break;case "name":td=na;break;case "formAction":ud=na;break;case "formEncType":vd=na;break;case "formMethod":wd=na;break;case "formTarget":xd=na;break;default:T(a,bb,na)}}var yd=bc(a,d,e,ud,vd,wd,xd,td);a.push(U);null!==yd&&yd.forEach($b,a);dc(a,sd,ab);if("string"===typeof ab){a.push(x(F(ab)));var zd=null}else zd=ab;return zd;case "form":a.push(V("form"));var cb=null,Ad=null,ta=null,db=null,eb=null,fb=null,gb;for(gb in c)if(C.call(c,gb)){var ua=c[gb];if(null!=ua)switch(gb){case "children":cb=ua;
break;case "dangerouslySetInnerHTML":Ad=ua;break;case "action":ta=ua;break;case "encType":db=ua;break;case "method":eb=ua;break;case "target":fb=ua;break;default:T(a,gb,ua)}}var lc=null,mc=null;if("function"===typeof ta)if("function"===typeof ta.$$FORM_ACTION){var pf=Xb(d),Ma=ta.$$FORM_ACTION(pf);ta=Ma.action||"";db=Ma.encType;eb=Ma.method;fb=Ma.target;lc=Ma.data;mc=Ma.name}else a.push(Q,x("action"),Ub,Yb,O),fb=eb=db=ta=null,cc(d,e);null!=ta&&T(a,"action",ta);null!=db&&T(a,"encType",db);null!=eb&&
T(a,"method",eb);null!=fb&&T(a,"target",fb);a.push(U);null!==mc&&(a.push(Zb),R(a,"name",mc),a.push(ac),null!==lc&&lc.forEach($b,a));dc(a,Ad,cb);if("string"===typeof cb){a.push(x(F(cb)));var Bd=null}else Bd=cb;return Bd;case "menuitem":a.push(V("menuitem"));for(var zb in c)if(C.call(c,zb)){var Cd=c[zb];if(null!=Cd)switch(zb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:T(a,zb,Cd)}}a.push(U);return null;case "title":if(3===
f.insertionMode||f.tagScope&1||null!=c.itemProp)var Dd=vc(a,c);else vc(e.hoistableChunks,c),Dd=null;return Dd;case "link":return jc(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var nc=c.async;if("string"!==typeof c.src||!c.src||!nc||"function"===typeof nc||"symbol"===typeof nc||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Ed=Jb(a,c);else{var Ab=c.src;if("module"===c.type){var Bb=d.moduleScriptResources;var Fd=e.preloads.moduleScripts}else Bb=d.scriptResources,
Fd=e.preloads.scripts;var Cb=Bb.hasOwnProperty(Ab)?Bb[Ab]:void 0;if(null!==Cb){Bb[Ab]=null;var oc=c;if(Cb){2===Cb.length&&(oc=A({},c),kc(oc,Cb));var Gd=Fd.get(Ab);Gd&&(Gd.length=0)}var Hd=[];e.scripts.add(Hd);Jb(Hd,oc)}g&&a.push(Nb);Ed=null}return Ed;case "style":var Db=c.precedence,za=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof Db||"string"!==typeof za||""===za){a.push(V("style"));var Na=null,Id=null,hb;for(hb in c)if(C.call(c,hb)){var Eb=c[hb];if(null!=Eb)switch(hb){case "children":Na=
Eb;break;case "dangerouslySetInnerHTML":Id=Eb;break;default:T(a,hb,Eb)}}a.push(U);var ib=Array.isArray(Na)?2>Na.length?Na[0]:null:Na;"function"!==typeof ib&&"symbol"!==typeof ib&&null!==ib&&void 0!==ib&&a.push(x(F(""+ib)));dc(a,Id,Na);a.push(wc("style"));var Jd=null}else{var Aa=e.styles.get(Db);if(null!==(d.styleResources.hasOwnProperty(za)?d.styleResources[za]:void 0)){d.styleResources[za]=null;Aa?Aa.hrefs.push(x(F(za))):(Aa={precedence:x(F(Db)),rules:[],hrefs:[x(F(za))],sheets:new Map},e.styles.set(Db,
Aa));var Kd=Aa.rules,Oa=null,Ld=null,Fb;for(Fb in c)if(C.call(c,Fb)){var pc=c[Fb];if(null!=pc)switch(Fb){case "children":Oa=pc;break;case "dangerouslySetInnerHTML":Ld=pc}}var jb=Array.isArray(Oa)?2>Oa.length?Oa[0]:null:Oa;"function"!==typeof jb&&"symbol"!==typeof jb&&null!==jb&&void 0!==jb&&Kd.push(x(F(""+jb)));dc(Kd,Ld,Oa)}Aa&&e.boundaryResources&&e.boundaryResources.styles.add(Aa);g&&a.push(Nb);Jd=void 0}return Jd;case "meta":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Md=uc(a,c,
"meta");else g&&a.push(Nb),Md="string"===typeof c.charSet?uc(e.charsetChunks,c,"meta"):"viewport"===c.name?uc(e.preconnectChunks,c,"meta"):uc(e.hoistableChunks,c,"meta");return Md;case "listing":case "pre":a.push(V(b));var kb=null,lb=null,mb;for(mb in c)if(C.call(c,mb)){var Gb=c[mb];if(null!=Gb)switch(mb){case "children":kb=Gb;break;case "dangerouslySetInnerHTML":lb=Gb;break;default:T(a,mb,Gb)}}a.push(U);if(null!=lb){if(null!=kb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");
if("object"!==typeof lb||!("__html"in lb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var Ba=lb.__html;null!==Ba&&void 0!==Ba&&("string"===typeof Ba&&0<Ba.length&&"\n"===Ba[0]?a.push(yc,x(Ba)):a.push(x(""+Ba)))}"string"===typeof kb&&"\n"===kb[0]&&a.push(yc);return kb;case "img":var L=c.src,I=c.srcSet;if(!("lazy"===c.loading||!L&&!I||"string"!==typeof L&&null!=L||"string"!==
typeof I&&null!=I)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof L||":"!==L[4]||"d"!==L[0]&&"D"!==L[0]||"a"!==L[1]&&"A"!==L[1]||"t"!==L[2]&&"T"!==L[2]||"a"!==L[3]&&"A"!==L[3])&&("string"!==typeof I||":"!==I[4]||"d"!==I[0]&&"D"!==I[0]||"a"!==I[1]&&"A"!==I[1]||"t"!==I[2]&&"T"!==I[2]||"a"!==I[3]&&"A"!==I[3])){var Nd="string"===typeof c.sizes?c.sizes:void 0,Pa=I?I+"\n"+(Nd||""):L,qc=e.preloads.images,Ca=qc.get(Pa);if(Ca){if("high"===c.fetchPriority||10>e.highImagePreloads.size)qc.delete(Pa),
e.highImagePreloads.add(Ca)}else if(!d.imageResources.hasOwnProperty(Pa)){d.imageResources[Pa]=J;var rc=c.crossOrigin;var Od="string"===typeof rc?"use-credentials"===rc?rc:"":void 0;var ha=e.headers,sc;ha&&0<ha.remainingCapacity&&("high"===c.fetchPriority||500>ha.highImagePreloads.length)&&(sc=Dc(L,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:Od,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.refererPolicy}),2<=(ha.remainingCapacity-=
sc.length))?(e.resets.image[Pa]=J,ha.highImagePreloads&&(ha.highImagePreloads+=", "),ha.highImagePreloads+=sc):(Ca=[],M(Ca,{rel:"preload",as:"image",href:I?void 0:L,imageSrcSet:I,imageSizes:Nd,crossOrigin:Od,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(Ca):(e.bulkPreloads.add(Ca),qc.set(Pa,Ca)))}}return uc(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return uc(a,
c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var Pd=xc(e.headChunks,c,"head")}else Pd=xc(a,c,"head");return Pd;case "html":if(0===f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[Bc];var Qd=xc(e.htmlChunks,c,"html")}else Qd=xc(a,c,"html");return Qd;default:if(-1!==b.indexOf("-")){a.push(V(b));
var tc=null,Rd=null,Qa;for(Qa in c)if(C.call(c,Qa)){var Da=c[Qa];if(null!=Da){var qf=Qa;switch(Qa){case "children":tc=Da;break;case "dangerouslySetInnerHTML":Rd=Da;break;case "style":Tb(a,Da);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:xa(Qa)&&"function"!==typeof Da&&"symbol"!==typeof Da&&a.push(Q,x(qf),Ub,x(F(Da)),O)}}}a.push(U);dc(a,Rd,tc);return tc}}return xc(a,c,b)}var Ec=new Map;
function wc(a){var b=Ec.get(a);void 0===b&&(b=z("</"+a+">"),Ec.set(a,b));return b}function Fc(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)p(a,b[c]);return c<b.length?(c=b[c],b.length=0,v(a,c)):!0}var Gc=z('<template id="'),Hc=z('"></template>'),Ic=z("\x3c!--$--\x3e"),Jc=z('\x3c!--$?--\x3e<template id="'),Kc=z('"></template>'),Lc=z("\x3c!--$!--\x3e"),Mc=z("\x3c!--/$--\x3e"),Nc=z("<template"),Oc=z('"'),Pc=z(' data-dgst="');z(' data-msg="');z(' data-stck="');var Qc=z("></template>");
function Rc(a,b,c){p(a,Jc);if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");p(a,b.boundaryPrefix);p(a,x(c.toString(16)));return v(a,Kc)}
var Sc=z('<div hidden id="'),Tc=z('">'),Uc=z("</div>"),Vc=z('<svg aria-hidden="true" style="display:none" id="'),Wc=z('">'),Xc=z("</svg>"),Yc=z('<math aria-hidden="true" style="display:none" id="'),Zc=z('">'),$c=z("</math>"),ad=z('<table hidden id="'),bd=z('">'),cd=z("</table>"),dd=z('<table hidden><tbody id="'),ed=z('">'),fd=z("</tbody></table>"),gd=z('<table hidden><tr id="'),hd=z('">'),id=z("</tr></table>"),jd=z('<table hidden><colgroup id="'),kd=z('">'),ld=z("</colgroup></table>");
function md(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return p(a,Sc),p(a,b.segmentPrefix),p(a,x(d.toString(16))),v(a,Tc);case 3:return p(a,Vc),p(a,b.segmentPrefix),p(a,x(d.toString(16))),v(a,Wc);case 4:return p(a,Yc),p(a,b.segmentPrefix),p(a,x(d.toString(16))),v(a,Zc);case 5:return p(a,ad),p(a,b.segmentPrefix),p(a,x(d.toString(16))),v(a,bd);case 6:return p(a,dd),p(a,b.segmentPrefix),p(a,x(d.toString(16))),v(a,ed);case 7:return p(a,gd),p(a,b.segmentPrefix),p(a,x(d.toString(16))),v(a,hd);
case 8:return p(a,jd),p(a,b.segmentPrefix),p(a,x(d.toString(16))),v(a,kd);default:throw Error("Unknown insertion mode. This is a bug in React.");}}function nd(a,b){switch(b.insertionMode){case 0:case 1:case 2:return v(a,Uc);case 3:return v(a,Xc);case 4:return v(a,$c);case 5:return v(a,cd);case 6:return v(a,fd);case 7:return v(a,id);case 8:return v(a,ld);default:throw Error("Unknown insertion mode. This is a bug in React.");}}
var od=z('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),pd=z('$RS("'),qd=z('","'),Sd=z('")\x3c/script>'),Td=z('<template data-rsi="" data-sid="'),Ud=z('" data-pid="'),Vd=z('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("'),
Wd=z('$RC("'),Xd=z('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Yd=z('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Zd=z('$RR("'),$d=z('","'),ae=z('",'),be=z('"'),ce=z(")\x3c/script>"),de=z('<template data-rci="" data-bid="'),ee=z('<template data-rri="" data-bid="'),fe=z('" data-sid="'),ge=z('" data-sty="'),he=z('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("'),ie=z('$RX("'),je=z('"'),ke=z(","),le=z(")\x3c/script>"),me=z('<template data-rxi="" data-bid="'),ne=z('" data-dgst="'),
oe=z('" data-msg="'),pe=z('" data-stck="'),qe=/[<\u2028\u2029]/g;function re(a){return JSON.stringify(a).replace(qe,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var se=/[&><\u2028\u2029]/g;
function te(a){return JSON.stringify(a).replace(se,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}
var ue=z('<style media="not all" data-precedence="'),ve=z('" data-href="'),we=z('">'),xe=z("</style>"),ye=!1,ze=!0;function Ae(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){p(this,ue);p(this,a.precedence);for(p(this,ve);d<c.length-1;d++)p(this,c[d]),p(this,Be);p(this,c[d]);p(this,we);for(d=0;d<b.length;d++)p(this,b[d]);ze=v(this,xe);ye=!0;b.length=0;c.length=0}}function Ce(a){return 2!==a.state?ye=!0:!1}
function De(a,b,c){ye=!1;ze=!0;b.styles.forEach(Ae,a);b.stylesheets.forEach(Ce);ye&&(c.stylesToHoist=!0);return ze}function Ee(a){for(var b=0;b<a.length;b++)p(this,a[b]);a.length=0}var Fe=[];function Ge(a){M(Fe,a.props);for(var b=0;b<Fe.length;b++)p(this,Fe[b]);Fe.length=0;a.state=2}var He=z('<style data-precedence="'),Ie=z('" data-href="'),Be=z(" "),Je=z('">'),Ke=z("</style>");
function Le(a){var b=0<a.sheets.size;a.sheets.forEach(Ge,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){p(this,He);p(this,a.precedence);a=0;if(d.length){for(p(this,Ie);a<d.length-1;a++)p(this,d[a]),p(this,Be);p(this,d[a])}p(this,Je);for(a=0;a<c.length;a++)p(this,c[a]);p(this,Ke);c.length=0;d.length=0}}
function Me(a){if(0===a.state){a.state=1;var b=a.props;M(Fe,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<Fe.length;a++)p(this,Fe[a]);Fe.length=0}}function Ne(a){a.sheets.forEach(Me,this);a.sheets.clear()}var Oe=z("["),Pe=z(",["),Qe=z(","),Re=z("]");
function Se(a,b){p(a,Oe);var c=Oe;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)p(a,c),p(a,x(te(""+d.props.href))),p(a,Re),c=Pe;else{p(a,c);var e=d.props["data-precedence"],f=d.props;p(a,x(te(""+d.props.href)));e=""+e;p(a,Qe);p(a,x(te(e)));for(var g in f)if(C.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!xa(g))break a;h=""+h}p(e,Qe);p(e,x(te(l)));p(e,Qe);p(e,x(te(h)))}}}p(a,
Re);c=Pe;d.state=3}});p(a,Re)}
function Te(a,b){p(a,Oe);var c=Oe;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)p(a,c),p(a,x(F(JSON.stringify(""+d.props.href)))),p(a,Re),c=Pe;else{p(a,c);var e=d.props["data-precedence"],f=d.props;p(a,x(F(JSON.stringify(""+d.props.href))));e=""+e;p(a,Qe);p(a,x(F(JSON.stringify(e))));for(var g in f)if(C.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:a:{e=
a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!xa(g))break a;h=""+h}p(e,Qe);p(e,x(F(JSON.stringify(l))));p(e,Qe);p(e,x(F(JSON.stringify(h))))}}}p(a,
Re);c=Pe;d.state=3}});p(a,Re)}function Ta(a){var b=Ue();if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(Ve,We)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],M(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}Xe(b)}}}
function Ua(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(Ve,We)+">; rel=preconnect";if("string"===typeof b){var l=(""+b).replace(Ye,Ze);h+='; crossorigin="'+l+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],M(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}Xe(c)}}}
function Va(a,b,c){var d=Ue();if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var l=c.fetchPriority}var n=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(n))return;e.imageResources[n]=J;e=f.headers;var q;e&&0<e.remainingCapacity&&"high"===l&&(q=Dc(a,b,c),2<=(e.remainingCapacity-=q.length))?(f.resets.image[n]=J,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=q):(e=[],M(e,A({rel:"preload",href:g?void 0:
a,as:b},c)),"high"===l?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(n,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];M(g,A({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?J:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
M(g,A({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?J:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=J;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(n=Dc(a,b,c),2<=(e.remainingCapacity-=n.length)))f.resets.font[a]=J,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=n;else switch(e=[],
a=A({rel:"preload",href:a,as:b},c),M(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}Xe(d)}}}
function Wa(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?J:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=J}M(f,A({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);Xe(c)}}}
function Xa(a,b,c){var d=Ue();if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:x(F(b)),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:A({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&kc(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),Xe(d))}}}
function Ya(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=A({src:a,async:!0},b),f&&(2===f.length&&kc(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Jb(a,b),Xe(c))}}}
function Za(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=A({src:a,type:"module",async:!0},b),f&&(2===f.length&&kc(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Jb(a,b),Xe(c))}}}function kc(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Dc(a,b,c){a=(""+a).replace(Ve,We);b=(""+b).replace(Ye,Ze);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)C.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(Ye,Ze)+'"'));return b}var Ve=/[<>\r\n]/g;
function We(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var Ye=/["';,\r\n]/g;
function Ze(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function $e(a){this.styles.add(a)}function af(a){this.stylesheets.add(a)}
var bf="function"===typeof AsyncLocalStorage,cf=bf?new AsyncLocalStorage:null,df=Symbol.for("react.element"),ef=Symbol.for("react.portal"),ff=Symbol.for("react.fragment"),gf=Symbol.for("react.strict_mode"),hf=Symbol.for("react.profiler"),jf=Symbol.for("react.provider"),kf=Symbol.for("react.context"),lf=Symbol.for("react.server_context"),mf=Symbol.for("react.forward_ref"),nf=Symbol.for("react.suspense"),of=Symbol.for("react.suspense_list"),rf=Symbol.for("react.memo"),sf=Symbol.for("react.lazy"),tf=
Symbol.for("react.scope"),uf=Symbol.for("react.debug_trace_mode"),vf=Symbol.for("react.offscreen"),wf=Symbol.for("react.legacy_hidden"),xf=Symbol.for("react.cache"),yf=Symbol.for("react.default_value"),zf=Symbol.iterator;
function Af(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case ff:return"Fragment";case ef:return"Portal";case hf:return"Profiler";case gf:return"StrictMode";case nf:return"Suspense";case of:return"SuspenseList";case xf:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case kf:return(a.displayName||"Context")+".Consumer";case jf:return(a._context.displayName||"Context")+".Provider";case mf:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case rf:return b=a.displayName||null,null!==b?b:Af(a.type)||"Memo";case sf:b=a._payload;a=a._init;try{return Af(a(b))}catch(c){}}return null}var Bf={};function Cf(a,b){a=a.contextTypes;if(!a)return Bf;var c={},d;for(d in a)c[d]=b[d];return c}var Df=null;
function Ef(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Ef(a,c)}b.context._currentValue=b.value}}function Ff(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ff(a)}
function Gf(a){var b=a.parent;null!==b&&Gf(b);a.context._currentValue=a.value}function Hf(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Ef(a,b):Hf(a,b)}
function If(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?Ef(a,c):If(a,c);b.context._currentValue=b.value}function Jf(a){var b=Df;b!==a&&(null===b?Gf(a):null===a?Ff(b):b.depth===a.depth?Ef(b,a):b.depth>a.depth?Hf(b,a):If(b,a),Df=a)}
var Kf={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Lf(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Kf;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:A({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Kf.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=A({},f,h)):A(f,h))}a.state=f}else f.queue=null}
var Mf={id:1,overflow:""};function Nf(a,b,c){var d=a.id;a=a.overflow;var e=32-Of(d)-1;d&=~(1<<e);c+=1;var f=32-Of(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Of(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Of=Math.clz32?Math.clz32:Pf,Qf=Math.log,Rf=Math.LN2;function Pf(a){a>>>=0;return 0===a?32:31-(Qf(a)/Rf|0)|0}var Sf=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Tf(){}function Uf(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Tf,Tf),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Vf=b;throw Sf;}}var Vf=null;
function Wf(){if(null===Vf)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Vf;Vf=null;return a}function Xf(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Yf="function"===typeof Object.is?Object.is:Xf,Zf=null,$f=null,ag=null,bg=null,cg=null,X=null,dg=!1,eg=!1,fg=0,gg=0,hg=-1,ig=0,jg=null,kg=null,lg=0;
function mg(){if(null===Zf)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return Zf}
function ng(){if(0<lg)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function og(){null===X?null===cg?(dg=!1,cg=X=ng()):(dg=!0,X=cg):null===X.next?(dg=!1,X=X.next=ng()):(dg=!0,X=X.next);return X}function pg(a,b,c,d){for(;eg;)eg=!1,gg=fg=0,hg=-1,ig=0,lg+=1,X=null,c=a(b,d);qg();return c}function rg(){var a=jg;jg=null;return a}function qg(){bg=ag=$f=Zf=null;eg=!1;cg=null;lg=0;X=kg=null}
function sg(a,b){return"function"===typeof b?b(a):b}function tg(a,b,c){Zf=mg();X=og();if(dg){var d=X.queue;b=d.dispatch;if(null!==kg&&(c=kg.get(d),void 0!==c)){kg.delete(d);d=X.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);X.memoizedState=d;return[d,b]}return[X.memoizedState,b]}a=a===sg?"function"===typeof b?b():b:void 0!==c?c(b):b;X.memoizedState=a;a=X.queue={last:null,dispatch:null};a=a.dispatch=ug.bind(null,Zf,a);return[X.memoizedState,a]}
function vg(a,b){Zf=mg();X=og();b=void 0===b?null:b;if(null!==X){var c=X.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Yf(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();X.memoizedState=[a,b];return a}
function ug(a,b,c){if(25<=lg)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===Zf)if(eg=!0,a={action:c,next:null},null===kg&&(kg=new Map),c=kg.get(b),void 0===c)kg.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function wg(){throw Error("startTransition cannot be called during server rendering.");}function xg(){throw Error("Cannot update optimistic state while rendering.");}
function yg(a){var b=ig;ig+=1;null===jg&&(jg=[]);return Uf(jg,a,b)}function zg(){throw Error("Cache cannot be refreshed during server rendering.");}function Ag(){}
var Cg={readContext:function(a){return a._currentValue},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return yg(a);if(a.$$typeof===kf||a.$$typeof===lf)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){mg();return a._currentValue},useMemo:vg,useReducer:tg,useRef:function(a){Zf=mg();X=og();var b=X.memoizedState;return null===b?(a={current:a},X.memoizedState=a):b},useState:function(a){return tg(sg,a)},
useInsertionEffect:Ag,useLayoutEffect:Ag,useCallback:function(a,b){return vg(function(){return a},b)},useImperativeHandle:Ag,useEffect:Ag,useDebugValue:Ag,useDeferredValue:function(a){mg();return a},useTransition:function(){mg();return[!1,wg]},useId:function(){var a=$f.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Of(a)-1)).toString(32)+b;var c=Bg;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=fg++;a=":"+c.idPrefix+"R"+a;0<b&&
(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return zg},useHostTransitionStatus:function(){mg();return Ra},useOptimistic:function(a){mg();return[a,xg]},useFormState:function(a,b,c){mg();var d=gg++,e=ag;if("function"===typeof a.$$FORM_ACTION){var f=null,g=bg;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;
if(null!==e&&"function"===typeof h){var l=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+da(JSON.stringify([g,null,d]),0),l===f&&(hg=d,b=e[0]))}var n=a.bind(null,b);a=function(t){n(t)};"function"===typeof n.$$FORM_ACTION&&(a.$$FORM_ACTION=function(t){t=n.$$FORM_ACTION(t);void 0!==c&&(c+="",t.action=c);var r=t.data;r&&(null===f&&(f=void 0!==c?"p"+c:"k"+da(JSON.stringify([g,null,d]),0)),r.append("$ACTION_KEY",f));return t});return[b,a]}var q=a.bind(null,b);return[b,function(t){q(t)}]}},Bg=null,Dg=
{getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");}},Eg=Ka.ReactCurrentDispatcher,Fg=Ka.ReactCurrentCache;function Gg(a){console.error(a);return null}function Hg(){}
function Ig(a,b,c,d,e,f,g,h,l,n,q,t){Sa.current=$a;var r=[],w=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:w,pingedTasks:r,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?Gg:f,onPostpone:void 0===q?Hg:q,onAllReady:void 0===g?
Hg:g,onShellReady:void 0===h?Hg:h,onShellError:void 0===l?Hg:l,onFatalError:void 0===n?Hg:n,formState:void 0===t?null:t};c=Jg(b,0,null,d,!1,!1);c.parentFlushed=!0;a=Kg(b,null,a,-1,null,c,w,null,d,Bf,null,Mf);r.push(a);return b}var Lg=null;function Ue(){if(Lg)return Lg;if(bf){var a=cf.getStore();if(a)return a}return null}function Mg(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return Ng(a)},0))}
function Og(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function Kg(a,b,c,d,e,f,g,h,l,n,q,t){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var r={replay:null,node:c,childIndex:d,ping:function(){return Mg(a,r)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:l,legacyContext:n,context:q,treeContext:t,thenableState:b};g.add(r);return r}
function Pg(a,b,c,d,e,f,g,h,l,n,q,t){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var r={replay:c,node:d,childIndex:e,ping:function(){return Mg(a,r)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:l,legacyContext:n,context:q,treeContext:t,thenableState:b};g.add(r);return r}function Jg(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function Y(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function Qg(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,oa(a.destination,b)):(a.status=1,a.fatalError=b)}
function Rg(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((Af(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=A({},c,d)}b.legacyContext=e;Z(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Z(a,b,null,f,-1),b.keyPath=e}
function Sg(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var l=b.blockedSegment;if(null!==l){h=!0;l=l.chunks;for(var n=0;n<f;n++)n===g?l.push(hc):l.push(ic)}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Nf(c,1,0),Tg(a,b,d,-1),b.treeContext=c):h?Tg(a,b,d,-1):Z(a,b,null,d,-1);b.keyPath=f}function Ug(a,b){if(a&&a.defaultProps){b=A({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function Vg(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=Cf(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue:d);Lf(h,e,f,d);Rg(a,b,c,h,e)}else{h=Cf(e,b.legacyContext);Zf={};$f=b;ag=a;bg=c;gg=fg=0;hg=-1;ig=0;jg=d;d=e(f,h);d=pg(e,f,d,h);g=0!==fg;var l=gg,n=hg;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(Lf(d,e,f,h),Rg(a,b,c,d,e)):Sg(a,b,c,d,g,l,n)}else if("string"===typeof e)if(d=
b.blockedSegment,null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=Mb(h,e,f),b.keyPath=c,Tg(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=Cc(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;l=b.keyPath;b.formatContext=Mb(h,e,f);b.keyPath=c;Tg(a,b,g,-1);b.formatContext=h;b.keyPath=l;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push(wc(e))}d.lastPushedText=!1}else{switch(e){case wf:case uf:case gf:case hf:case ff:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case vf:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Z(a,b,null,f.children,-1),b.keyPath=e);return;case of:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case tf:throw Error("ReactDOMServer does not yet support scope components.");
case nf:a:if(null!==b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{Tg(a,b,c,-1)}finally{b.keyPath=e}}else{n=b.keyPath;e=b.blockedBoundary;var q=b.blockedSegment;d=f.fallback;var t=f.children;f=new Set;g=Og(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);l=Jg(a,q.chunks.length,g,b.formatContext,!1,!1);q.children.push(l);q.lastPushedText=!1;var r=Jg(a,0,null,b.formatContext,!1,!1);r.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=r;a.renderState.boundaryResources=g.resources;b.keyPath=
c;try{if(Tg(a,b,t,-1),r.lastPushedText&&r.textEmbedded&&r.chunks.push(Nb),r.status=1,Wg(g,r),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(w){r.status=4,g.status=4,h=Y(a,w),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=q,b.keyPath=n}h=[c[0],"Suspense Fallback",c[2]];n=a.trackedPostpones;null!==n&&(q=[h[1],h[2],[],null],n.workingMap.set(h,q),5===g.status?n.workingMap.get(c)[4]=q:g.trackedFallbackNode=q);b=Kg(a,null,d,-1,
e,l,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case mf:e=e.render;Zf={};$f=b;ag=a;bg=c;gg=fg=0;hg=-1;ig=0;jg=d;d=e(f,g);f=pg(e,f,d,g);Sg(a,b,c,f,0!==fg,gg,hg);return;case rf:e=e.type;f=Ug(e,f);Vg(a,b,c,d,e,f,g);return;case jf:h=f.children;d=b.keyPath;e=e._context;f=f.value;g=e._currentValue;e._currentValue=f;l=Df;Df=f={parent:l,depth:null===l?0:l.depth+1,context:e,parentValue:g,value:f};b.context=f;
b.keyPath=c;Z(a,b,null,h,-1);a=Df;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===yf?a.context._defaultValue:c;a=Df=a.parent;b.context=a;b.keyPath=d;return;case kf:f=f.children;f=f(e._currentValue);e=b.keyPath;b.keyPath=c;Z(a,b,null,f,-1);b.keyPath=e;return;case sf:h=e._init;e=h(e._payload);f=Ug(e,f);Vg(a,b,c,d,e,f,void 0);return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+
((null==e?e:typeof e)+"."));}}function Xg(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=Jg(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Tg(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(Wg(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Z(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)Xg(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case df:var f=d.type,g=d.key,h=d.props,l=d.ref,n=Af(f),q=null==g?-1===e?0:e:g;g=[b.keyPath,n,q];if(null!==b.replay)a:{var t=b.replay;e=t.nodes;for(d=0;d<e.length;d++){var r=e[d];if(q===r[1]){if(4===r.length){if(null!==n&&n!==r[0])throw Error("Expected the resume to render <"+r[0]+"> in this slot but instead it rendered <"+
n+">. The tree doesn't match so React will fallback to client rendering.");n=r[2];r=r[3];q=b.node;b.replay={nodes:n,slots:r,pendingTasks:1};try{Vg(a,b,g,c,f,h,l);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(G){if("object"===typeof G&&null!==G&&(G===Sf||"function"===typeof G.then))throw b.node===q&&(b.replay=t),G;b.replay.pendingTasks--;
g=a;a=b.blockedBoundary;c=G;h=Y(g,c);Yg(g,a,n,r,c,h)}b.replay=t}else{if(f!==nf)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(Af(f)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{t=void 0;c=r[5];f=r[2];l=r[3];n=null===r[4]?[]:r[4][2];r=null===r[4]?null:r[4][3];q=b.keyPath;var w=b.replay,y=b.blockedBoundary,K=h.children;h=h.fallback;var u=new Set,B=Og(a,u);B.parentFlushed=!0;B.rootSegmentID=c;b.blockedBoundary=
B;b.replay={nodes:f,slots:l,pendingTasks:1};a.renderState.boundaryResources=B.resources;try{Tg(a,b,K,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===B.pendingTasks&&0===B.status){B.status=1;a.completedBoundaries.push(B);break b}}catch(G){B.status=4,t=Y(a,G),B.errorDigest=t,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(B)}finally{a.renderState.boundaryResources=
y?y.resources:null,b.blockedBoundary=y,b.replay=w,b.keyPath=q}b=Pg(a,null,{nodes:n,slots:r,pendingTasks:0},h,-1,y,u,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else Vg(a,b,g,c,f,h,l);return;case ef:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");case sf:h=d._init;d=h(d._payload);Z(a,b,null,d,e);return}if(Ja(d)){Zg(a,
b,d,e);return}null===d||"object"!==typeof d?h=null:(h=zf&&d[zf]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);Zg(a,b,g,e)}return}if("function"===typeof d.then)return Z(a,b,null,yg(d),e);if(d.$$typeof===kf||d.$$typeof===lf)return Z(a,b,null,d._currentValue,e);e=Object.prototype.toString.call(d);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+
"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=Ob(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=Ob(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function Zg(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var l=g[h];if(l[1]===d){d=l[2];l=l[3];b.replay={nodes:d,slots:l,pendingTasks:1};try{Zg(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(t){if("object"===typeof t&&
null!==t&&(t===Sf||"function"===typeof t.then))throw t;b.replay.pendingTasks--;c=a;var n=b.blockedBoundary,q=t;a=Y(c,q);Yg(c,n,d,l,q,a)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)l=c[d],b.treeContext=Nf(f,g,d),n=h[d],"number"===typeof n?(Xg(a,b,n,l,d),delete h[d]):Tg(a,b,l,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Nf(f,g,h),Tg(a,b,d,h);b.treeContext=
f;b.keyPath=e}
function Tg(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,l=b.treeContext,n=b.blockedSegment;if(null===n)try{return Z(a,b,null,c,d)}catch(r){if(qg(),c=r===Sf?Wf():r,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=rg();a=Pg(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;Jf(g);return}}else{var q=
n.children.length,t=n.chunks.length;try{return Z(a,b,null,c,d)}catch(r){if(qg(),n.children.length=q,n.chunks.length=t,c=r===Sf?Wf():r,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=rg();n=b.blockedSegment;q=Jg(a,n.chunks.length,null,b.formatContext,n.lastPushedText,!0);n.children.push(q);n.lastPushedText=!1;a=Kg(a,d,b.node,b.childIndex,b.blockedBoundary,q,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=l;Jf(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;Jf(g);throw c;}function $g(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,ah(this,b,a))}
function Yg(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)Yg(a,b,h[2],h[3],e,f);else{h=h[5];var l=a,n=f,q=Og(l,new Set);q.parentFlushed=!0;q.rootSegmentID=h;q.status=4;q.errorDigest=n;q.parentFlushed&&l.clientRenderedBoundaries.push(q)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var t in d)delete d[t]}}
function bh(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(1!==b.status&&2!==b.status){a=a.replay;if(null===a){Y(b,c);Qg(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=Y(b,c),Yg(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&ch(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=Y(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return bh(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&dh(b)}
function eh(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var l=h.value.sheets.values(),n=l.next();0<e.remainingCapacity&&!n.done;n=l.next()){var q=n.value,t=q.props,r=t.href,w=q.props,y=Dc(w.href,"style",{crossOrigin:w.crossOrigin,integrity:w.integrity,
nonce:w.nonce,type:w.type,fetchPriority:w.fetchPriority,referrerPolicy:w.referrerPolicy,media:w.media});if(2<=(e.remainingCapacity-=y.length))c.resets.style[r]=J,f&&(f+=", "),f+=y,c.resets.style[r]="string"===typeof t.crossOrigin||"string"===typeof t.integrity?[t.crossOrigin,t.integrity]:J;else break b}}f?d({Link:f}):d({})}}}catch(K){Y(a,K)}}function ch(a){null===a.trackedPostpones&&eh(a,!0);a.onShellError=Hg;a=a.onShellReady;a()}
function dh(a){eh(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function Wg(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&Wg(a,c)}else a.completedSegments.push(b)}
function ah(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&ch(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&Wg(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach($g,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(Wg(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&dh(a)}
function Ng(a){if(2!==a.status){var b=Df,c=Eg.current;Eg.current=Cg;var d=Fg.current;Fg.current=Dg;var e=Lg;Lg=a;var f=Bg;Bg=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var l=g[h],n=a,q=l.blockedBoundary;n.renderState.boundaryResources=q?q.resources:null;var t=l.blockedSegment;if(null===t){var r=n;if(0!==l.replay.pendingTasks){Jf(l.context);try{var w=l.thenableState;l.thenableState=null;Z(r,l,w,l.node,l.childIndex);if(1===l.replay.pendingTasks&&0<l.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
l.replay.pendingTasks--;l.abortSet.delete(l);ah(r,l.blockedBoundary,null)}catch(H){qg();var y=H===Sf?Wf():H;if("object"===typeof y&&null!==y&&"function"===typeof y.then){var K=l.ping;y.then(K,K);l.thenableState=rg()}else{l.replay.pendingTasks--;l.abortSet.delete(l);n=void 0;var u=r,B=l.blockedBoundary,G=y,ka=l.replay.nodes,D=l.replay.slots;n=Y(u,G);Yg(u,B,ka,D,G,n);r.pendingRootTasks--;0===r.pendingRootTasks&&ch(r);r.allPendingTasks--;0===r.allPendingTasks&&dh(r)}}finally{r.renderState.boundaryResources=
null}}}else if(r=void 0,u=t,0===u.status){Jf(l.context);var W=u.children.length,E=u.chunks.length;try{var ea=l.thenableState;l.thenableState=null;Z(n,l,ea,l.node,l.childIndex);u.lastPushedText&&u.textEmbedded&&u.chunks.push(Nb);l.abortSet.delete(l);u.status=1;ah(n,l.blockedBoundary,u)}catch(H){qg();u.children.length=W;u.chunks.length=E;var S=H===Sf?Wf():H;if("object"===typeof S&&null!==S&&"function"===typeof S.then){var la=l.ping;S.then(la,la);l.thenableState=rg()}else{l.abortSet.delete(l);u.status=
4;var P=l.blockedBoundary;r=Y(n,S);null===P?Qg(n,S):(P.pendingTasks--,4!==P.status&&(P.status=4,P.errorDigest=r,P.parentFlushed&&n.clientRenderedBoundaries.push(P)));n.allPendingTasks--;0===n.allPendingTasks&&dh(n)}}finally{n.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&fh(a,a.destination)}catch(H){Y(a,H),Qg(a,H)}finally{Bg=f,Eg.current=c,Fg.current=d,c===Cg&&Jf(b),Lg=e}}}
function gh(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;p(b,Gc);p(b,a.placeholderPrefix);a=x(d.toString(16));p(b,a);return v(b,Hc);case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)p(b,d[f]);e=hh(a,b,e)}for(;f<d.length-1;f++)p(b,d[f]);f<d.length&&(e=v(b,d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function hh(a,b,c){var d=c.boundary;if(null===d)return gh(a,b,c);d.parentFlushed=!0;if(4===d.status)d=d.errorDigest,v(b,Lc),p(b,Nc),d&&(p(b,Pc),p(b,x(F(d))),p(b,Oc)),v(b,Qc),gh(a,b,c);else if(1!==d.status)0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Rc(b,a.renderState,d.rootSegmentID),gh(a,b,c);else if(d.byteSize>a.progressiveChunkSize)d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Rc(b,a.renderState,d.rootSegmentID),gh(a,
b,c);else{c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach($e,e),c.stylesheets.forEach(af,e));v(b,Ic);d=d.completedSegments;if(1!==d.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");hh(a,b,d[0])}return v(b,Mc)}function ih(a,b,c){md(b,a.renderState,c.parentFormatContext,c.id);hh(a,b,c);return nd(b,c.parentFormatContext)}
function jh(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)kh(a,b,c,d[e]);d.length=0;De(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(p(b,a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,p(b,512<Xd.byteLength?Xd.slice():Xd)):0===(d.instructions&8)?(d.instructions|=8,p(b,Yd)):p(b,Zd):0===(d.instructions&2)?(d.instructions|=
2,p(b,Vd)):p(b,Wd)):f?p(b,ee):p(b,de);d=x(e.toString(16));p(b,a.boundaryPrefix);p(b,d);g?p(b,$d):p(b,fe);p(b,a.segmentPrefix);p(b,d);f?g?(p(b,ae),Se(b,c)):(p(b,ge),Te(b,c)):g&&p(b,be);d=g?v(b,ce):v(b,nb);return Fc(b,a)&&d}
function kh(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return ih(a,b,d)}if(e===c.rootSegmentID)return ih(a,b,d);ih(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(p(b,a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,p(b,od)):p(b,pd)):p(b,Td);p(b,a.segmentPrefix);e=x(e.toString(16));p(b,e);d?p(b,qd):p(b,Ud);p(b,a.placeholderPrefix);
p(b,e);b=d?v(b,Sd):v(b,nb);return b}
function fh(a,b){k=new Uint8Array(512);m=0;try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,l=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(l))}var n=e.htmlChunks,q=e.headChunks;f=0;if(n){for(f=0;f<n.length;f++)p(b,n[f]);if(q)for(f=0;f<q.length;f++)p(b,q[f]);else p(b,
V("head")),p(b,U)}else if(q)for(f=0;f<q.length;f++)p(b,q[f]);var t=e.charsetChunks;for(f=0;f<t.length;f++)p(b,t[f]);t.length=0;e.preconnects.forEach(Ee,b);e.preconnects.clear();var r=e.preconnectChunks;for(f=0;f<r.length;f++)p(b,r[f]);r.length=0;e.fontPreloads.forEach(Ee,b);e.fontPreloads.clear();e.highImagePreloads.forEach(Ee,b);e.highImagePreloads.clear();e.styles.forEach(Le,b);var w=e.importMapChunks;for(f=0;f<w.length;f++)p(b,w[f]);w.length=0;e.bootstrapScripts.forEach(Ee,b);e.scripts.forEach(Ee,
b);e.scripts.clear();e.bulkPreloads.forEach(Ee,b);e.bulkPreloads.clear();var y=e.preloadChunks;for(f=0;f<y.length;f++)p(b,y[f]);y.length=0;var K=e.hoistableChunks;for(f=0;f<K.length;f++)p(b,K[f]);K.length=0;n&&null===q&&p(b,wc("head"));hh(a,b,d);a.completedRootSegment=null;Fc(b,a.renderState)}else return;var u=a.renderState;d=0;u.preconnects.forEach(Ee,b);u.preconnects.clear();var B=u.preconnectChunks;for(d=0;d<B.length;d++)p(b,B[d]);B.length=0;u.fontPreloads.forEach(Ee,b);u.fontPreloads.clear();
u.highImagePreloads.forEach(Ee,b);u.highImagePreloads.clear();u.styles.forEach(Ne,b);u.scripts.forEach(Ee,b);u.scripts.clear();u.bulkPreloads.forEach(Ee,b);u.bulkPreloads.clear();var G=u.preloadChunks;for(d=0;d<G.length;d++)p(b,G[d]);G.length=0;var ka=u.hoistableChunks;for(d=0;d<ka.length;d++)p(b,ka[d]);ka.length=0;var D=a.clientRenderedBoundaries;for(c=0;c<D.length;c++){var W=D[c];u=b;var E=a.resumableState,ea=a.renderState,S=W.rootSegmentID,la=W.errorDigest,P=W.errorMessage,H=W.errorComponentStack,
ma=0===E.streamingFormat;ma?(p(u,ea.startInlineScript),0===(E.instructions&4)?(E.instructions|=4,p(u,he)):p(u,ie)):p(u,me);p(u,ea.boundaryPrefix);p(u,x(S.toString(16)));ma&&p(u,je);if(la||P||H)ma?(p(u,ke),p(u,x(re(la||"")))):(p(u,ne),p(u,x(F(la||""))));if(P||H)ma?(p(u,ke),p(u,x(re(P||"")))):(p(u,oe),p(u,x(F(P||""))));H&&(ma?(p(u,ke),p(u,x(re(H)))):(p(u,pe),p(u,x(F(H)))));if(ma?!v(u,le):!v(u,nb)){a.destination=null;c++;D.splice(0,c);return}}D.splice(0,c);var ya=a.completedBoundaries;for(c=0;c<ya.length;c++)if(!jh(a,
b,ya[c])){a.destination=null;c++;ya.splice(0,c);return}ya.splice(0,c);ia(b);k=new Uint8Array(512);m=0;var qa=a.partialBoundaries;for(c=0;c<qa.length;c++){var ra=qa[c];a:{D=a;W=b;D.renderState.boundaryResources=ra.resources;var sa=ra.completedSegments;for(E=0;E<sa.length;E++)if(!kh(D,W,ra,sa[E])){E++;sa.splice(0,E);var La=!1;break a}sa.splice(0,E);La=De(W,ra.resources,D.renderState)}if(!La){a.destination=null;c++;qa.splice(0,c);return}}qa.splice(0,c);var fa=a.completedBoundaries;for(c=0;c<fa.length;c++)if(!jh(a,
b,fa[c])){a.destination=null;c++;fa.splice(0,c);return}fa.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length?(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&p(b,wc("body")),c.hasHtml&&p(b,wc("html")),ia(b),b.close(),a.destination=null):ia(b)}}
function lh(a){a.flushScheduled=null!==a.destination;bf?setTimeout(function(){return cf.run(a,Ng,a)},0):setTimeout(function(){return Ng(a)},0);null===a.trackedPostpones&&(bf?setTimeout(function(){return cf.run(a,mh,a)},0):setTimeout(function(){return mh(a)},0))}function mh(a){eh(a,0===a.pendingRootTasks)}function Xe(a){!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(a.flushScheduled=!0,setTimeout(function(){var b=a.destination;b?fh(a,b):a.flushScheduled=!1},0))}
function nh(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return bh(e,a,d)});c.clear()}null!==a.destination&&fh(a,a.destination)}catch(e){Y(a,e),Qg(a,e)}}
exports.renderToReadableStream=function(a,b){return new Promise(function(c,d){var e,f,g=new Promise(function(w,y){f=w;e=y}),h=b?b.onHeaders:void 0,l;h&&(l=function(w){h(new Headers(w))});var n=Kb(b?b.identifierPrefix:void 0,b?b.unstable_externalRuntimeSrc:void 0,b?b.bootstrapScriptContent:void 0,b?b.bootstrapScripts:void 0,b?b.bootstrapModules:void 0),q=Ig(a,n,Ib(n,b?b.nonce:void 0,b?b.unstable_externalRuntimeSrc:void 0,b?b.importMap:void 0,l,b?b.maxHeadersLength:void 0),Lb(b?b.namespaceURI:void 0),
b?b.progressiveChunkSize:void 0,b?b.onError:void 0,f,function(){var w=new ReadableStream({type:"bytes",pull:function(y){if(1===q.status)q.status=2,oa(y,q.fatalError);else if(2!==q.status&&null===q.destination){q.destination=y;try{fh(q,y)}catch(K){Y(q,K),Qg(q,K)}}},cancel:function(y){q.destination=null;nh(q,y)}},{highWaterMark:0});w.allReady=g;c(w)},function(w){g.catch(function(){});d(w)},e,b?b.onPostpone:void 0,b?b.formState:void 0);if(b&&b.signal){var t=b.signal;if(t.aborted)nh(q,t.reason);else{var r=
function(){nh(q,t.reason);t.removeEventListener("abort",r)};t.addEventListener("abort",r)}}lh(q)})};exports.version="18.3.0-canary-2c338b16f-20231116";

//# sourceMappingURL=react-dom-server.edge.production.min.js.map
