{"version": 3, "sources": ["../../../src/client/components/app-router-announcer.tsx"], "names": ["useEffect", "useRef", "useState", "createPortal", "ANNOUNCER_TYPE", "ANNOUNCER_ID", "getAnnouncerNode", "existingAnnouncer", "document", "getElementsByName", "shadowRoot", "childNodes", "container", "createElement", "style", "cssText", "announcer", "ariaLive", "id", "role", "shadow", "attachShadow", "mode", "append<PERSON><PERSON><PERSON>", "body", "AppRouterAnnouncer", "tree", "portalNode", "setPortalNode", "getElementsByTagName", "isConnected", "<PERSON><PERSON><PERSON><PERSON>", "routeAnnouncement", "setRouteAnnouncement", "previousTitle", "currentTitle", "title", "pageHeader", "querySelector", "innerText", "textContent", "current", "undefined"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,QAAO;AACnD,SAASC,YAAY,QAAQ,YAAW;AAGxC,MAAMC,iBAAiB;AACvB,MAAMC,eAAe;AAErB,SAASC;QAEHC;IADJ,MAAMA,oBAAoBC,SAASC,iBAAiB,CAACL,eAAe,CAAC,EAAE;IACvE,IAAIG,sCAAAA,gCAAAA,kBAAmBG,UAAU,qBAA7BH,8BAA+BI,UAAU,CAAC,EAAE,EAAE;QAChD,OAAOJ,kBAAkBG,UAAU,CAACC,UAAU,CAAC,EAAE;IACnD,OAAO;QACL,MAAMC,YAAYJ,SAASK,aAAa,CAACT;QACzCQ,UAAUE,KAAK,CAACC,OAAO,GAAG;QAC1B,MAAMC,YAAYR,SAASK,aAAa,CAAC;QACzCG,UAAUC,QAAQ,GAAG;QACrBD,UAAUE,EAAE,GAAGb;QACfW,UAAUG,IAAI,GAAG;QACjBH,UAAUF,KAAK,CAACC,OAAO,GACrB;QAEF,uDAAuD;QACvD,MAAMK,SAASR,UAAUS,YAAY,CAAC;YAAEC,MAAM;QAAO;QACrDF,OAAOG,WAAW,CAACP;QACnBR,SAASgB,IAAI,CAACD,WAAW,CAACX;QAC1B,OAAOI;IACT;AACF;AAEA,OAAO,SAASS,mBAAmB,KAAqC;IAArC,IAAA,EAAEC,IAAI,EAA+B,GAArC;IACjC,MAAM,CAACC,YAAYC,cAAc,GAAG1B,SAA6B;IAEjEF,UAAU;QACR,MAAMgB,YAAYV;QAClBsB,cAAcZ;QACd,OAAO;YACL,MAAMJ,YAAYJ,SAASqB,oBAAoB,CAACzB,eAAe,CAAC,EAAE;YAClE,IAAIQ,6BAAAA,UAAWkB,WAAW,EAAE;gBAC1BtB,SAASgB,IAAI,CAACO,WAAW,CAACnB;YAC5B;QACF;IACF,GAAG,EAAE;IAEL,MAAM,CAACoB,mBAAmBC,qBAAqB,GAAG/B,SAAS;IAC3D,MAAMgC,gBAAgBjC;IAEtBD,UAAU;QACR,IAAImC,eAAe;QACnB,IAAI3B,SAAS4B,KAAK,EAAE;YAClBD,eAAe3B,SAAS4B,KAAK;QAC/B,OAAO;YACL,MAAMC,aAAa7B,SAAS8B,aAAa,CAAC;YAC1C,IAAID,YAAY;gBACdF,eAAeE,WAAWE,SAAS,IAAIF,WAAWG,WAAW,IAAI;YACnE;QACF;QAEA,4EAA4E;QAC5E,iCAAiC;QACjC,IACEN,cAAcO,OAAO,KAAKC,aAC1BR,cAAcO,OAAO,KAAKN,cAC1B;YACAF,qBAAqBE;QACvB;QACAD,cAAcO,OAAO,GAAGN;IAC1B,GAAG;QAACT;KAAK;IAET,OAAOC,2BAAaxB,aAAa6B,mBAAmBL,cAAc;AACpE"}