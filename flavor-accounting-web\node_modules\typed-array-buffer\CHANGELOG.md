# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.3](https://github.com/inspect-js/typed-array-buffer/compare/v1.0.2...v1.0.3) - 2024-12-18

### Commits

- [meta] update URLs [`aca9484`](https://github.com/inspect-js/typed-array-buffer/commit/aca9484b41f96767408e26e63854b5d86f759de8)
- [types] use shared config [`fcdcb05`](https://github.com/inspect-js/typed-array-buffer/commit/fcdcb05941a771826e1478a77aadd89c582e37cd)
- [actions] split out node 10-20, and 20+ [`5f5a406`](https://github.com/inspect-js/typed-array-buffer/commit/5f5a4067752d7bccecbaa8f6e143863d55197af9)
- [types] improve types [`f45042c`](https://github.com/inspect-js/typed-array-buffer/commit/f45042c07c04007217404d73aa77c26a73885210)
- [Dev Deps] update `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/object-inspect`, `@types/tape`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`0c937e7`](https://github.com/inspect-js/typed-array-buffer/commit/0c937e72e93dccc359c08cf1a9ef060e5f5e1a8d)
- [Refactor] use `call-bound` directly [`cf4aba4`](https://github.com/inspect-js/typed-array-buffer/commit/cf4aba4d8c1702ee9130abaf8a6a72907ca96ce0)
- [Tests] replace `aud` with `npm audit` [`a3abb73`](https://github.com/inspect-js/typed-array-buffer/commit/a3abb739300d1de6e88736019d718d831c7a4cca)
- [Dev Deps] update `@types/tape` [`548ffdc`](https://github.com/inspect-js/typed-array-buffer/commit/548ffdc881726b060ac92fc0c59ab0bb150df91f)
- [Deps] update `is-typed-array` [`3b5deb1`](https://github.com/inspect-js/typed-array-buffer/commit/3b5deb191a1c942deced0273b07fe69bc8de39ab)
- [Deps] update `call-bind` [`02cbc0c`](https://github.com/inspect-js/typed-array-buffer/commit/02cbc0cca2f69d81cdeedf7beebae2a5dd9dd4f7)
- [Tests] add attw and `postlint` [`f6daa66`](https://github.com/inspect-js/typed-array-buffer/commit/f6daa6695a69878d845070b90ab0bbf6392ebb03)
- [Dev Deps] add missing peer dep [`c9faf2a`](https://github.com/inspect-js/typed-array-buffer/commit/c9faf2ac04fc78410aeb144405db110fe9b60b6c)

## [v1.0.2](https://github.com/inspect-js/typed-array-buffer/compare/v1.0.1...v1.0.2) - 2024-02-19

### Commits

- add types [`23c6fba`](https://github.com/inspect-js/typed-array-buffer/commit/23c6fba167dbc8c1e9291eed3f68e64a5651075a)
- [Deps] update `available-typed-arrays` [`5f68ba1`](https://github.com/inspect-js/typed-array-buffer/commit/5f68ba1fdcd004af46d529fbb08220de2254cf43)
- [Deps] update `call-bind` [`54a92ce`](https://github.com/inspect-js/typed-array-buffer/commit/54a92ce4caf023c8680ffe64534ba881b78cdc17)
- [Dev Deps] update `tape` [`b0b3342`](https://github.com/inspect-js/typed-array-buffer/commit/b0b3342bcbefae5f3dff01b0e3734b08ca927f58)

## [v1.0.1](https://github.com/inspect-js/typed-array-buffer/compare/v1.0.0...v1.0.1) - 2024-02-06

### Commits

- [Dev Deps] update `aud`, `available-typed-arrays`, `npmignore`, `object-inspect`, `tape` [`5334477`](https://github.com/inspect-js/typed-array-buffer/commit/53344773866f35820dc4deef1aa47ec7890f2b02)
- [Refactor] use `es-errors`, so things that only need those do not need `get-intrinsic` [`e2511e0`](https://github.com/inspect-js/typed-array-buffer/commit/e2511e011a2331bd4a36ad6003a98b1cf766bc26)
- [Deps] update `call-bind`, `get-intrinsic`, `is-typed-array` [`36c3b11`](https://github.com/inspect-js/typed-array-buffer/commit/36c3b11efc9bce98de8bee5f81dcae4305876893)
- [meta] add `sideEffects` flag [`46cc1f4`](https://github.com/inspect-js/typed-array-buffer/commit/46cc1f4a8b8875fc6e84b33182602ec37655bbbd)

## v1.0.0 - 2023-06-05

### Commits

- Initial implementation, tests, readme [`5bc2953`](https://github.com/inspect-js/typed-array-buffer/commit/5bc295337b4310659832fc08699a4d10c2dbbded)
- Initial commit [`98b8ac9`](https://github.com/inspect-js/typed-array-buffer/commit/98b8ac90f407c368effa25d395aeea1d72e1d4b6)
- npm init [`6a4a73c`](https://github.com/inspect-js/typed-array-buffer/commit/6a4a73c66b1f13fd17699c6500a4979003676696)
- Only apps should have lockfiles [`7226abf`](https://github.com/inspect-js/typed-array-buffer/commit/7226abfda329b99dc25526c48740b076d128a7be)
