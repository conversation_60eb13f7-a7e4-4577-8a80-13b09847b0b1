/*
 React
 react-dom-server.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var ba=require("util"),ca=require("crypto"),ha=require("async_hooks"),ia=require("next/dist/compiled/react"),ja=require("react-dom");function oa(a){"function"===typeof a.flush&&a.flush()}var k=null,m=0,pa=!0;
function u(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<m&&(va(a,k.subarray(0,m)),k=new Uint8Array(2048),m=0),va(a,wa.encode(b));else{var c=k;0<m&&(c=k.subarray(m));c=wa.encodeInto(b,c);var d=c.read;m+=c.written;d<b.length&&(va(a,k.subarray(0,m)),k=new Uint8Array(2048),m=wa.encodeInto(b.slice(d),k).written);2048===m&&(va(a,k),k=new Uint8Array(2048),m=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<m&&(va(a,k.subarray(0,m)),k=new Uint8Array(2048),m=0),va(a,b)):(c=k.length-m,c<
b.byteLength&&(0===c?va(a,k):(k.set(b.subarray(0,c),m),m+=c,va(a,k),b=b.subarray(c)),k=new Uint8Array(2048),m=0),k.set(b,m),m+=b.byteLength,2048===m&&(va(a,k),k=new Uint8Array(2048),m=0)))}function va(a,b){a=a.write(b);pa=pa&&a}function w(a,b){u(a,b);return pa}function xa(a){k&&0<m&&a.write(k.subarray(0,m));k=null;m=0;pa=!0}var wa=new ba.TextEncoder;function y(a){return wa.encode(a)}
var z=Object.assign,A=Object.prototype.hasOwnProperty,Ea=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Fa={},Ga={};
function Ha(a){if(A.call(Ga,a))return!0;if(A.call(Fa,a))return!1;if(Ea.test(a))return Ga[a]=!0;Fa[a]=!0;return!1}
var Ia=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Ja=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ka=/["'&<>]/;
function B(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ka.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var La=/([A-Z])/g,Sa=/^ms-/,Ta=Array.isArray,Ua=ia.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Va={pending:!1,data:null,method:null,action:null},Wa=ja.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,qb={prefetchDNS:Xa,preconnect:Ya,preload:Za,preloadModule:$a,preinitStyle:ab,preinitScript:ob,preinitModuleScript:pb},D=[],rb=y('"></template>'),sb=y("<script>"),tb=y("\x3c/script>"),ub=y('<script src="'),vb=y('<script type="module" src="'),wb=y('" nonce="'),xb=y('" integrity="'),
yb=y('" crossorigin="'),zb=y('" async="">\x3c/script>'),Ab=/(<\/|<)(s)(cript)/gi;function Jb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}var Kb=y('<script type="importmap">'),Lb=y("\x3c/script>");function G(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function Mb(a,b,c){switch(b){case "noscript":return G(2,null,a.tagScope|1);case "select":return G(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return G(3,null,a.tagScope);case "picture":return G(2,null,a.tagScope|2);case "math":return G(4,null,a.tagScope);case "foreignObject":return G(2,null,a.tagScope);case "table":return G(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return G(6,null,a.tagScope);case "colgroup":return G(8,null,a.tagScope);case "tr":return G(7,null,a.tagScope)}return 5<=
a.insertionMode?G(2,null,a.tagScope):0===a.insertionMode?"html"===b?G(1,null,a.tagScope):G(2,null,a.tagScope):1===a.insertionMode?G(2,null,a.tagScope):a}var Nb=y("\x3c!-- --\x3e");function Ob(a,b,c,d){if(""===b)return d;d&&a.push(Nb);a.push(B(b));return!0}var Pb=new Map,Qb=y(' style="'),Rb=y(":"),Sb=y(";");
function Tb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(A.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=B(d);e=B((""+e).trim())}else f=Pb.get(d),void 0===f&&(f=y(B(d.replace(La,"-$1").toLowerCase().replace(Sa,"-ms-"))),Pb.set(d,f)),e="number"===typeof e?0===e||Ia.has(d)?""+e:e+"px":
B((""+e).trim());c?(c=!1,a.push(Qb,f,Rb,e)):a.push(Sb,f,Rb,e)}}c||a.push(K)}var M=y(" "),N=y('="'),K=y('"'),Ub=y('=""');function Vb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(M,b,Ub)}function O(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(M,b,N,B(c),K)}function Wb(a){var b=a.nextFormID++;return a.idPrefix+b}var Xb=y(B("javascript:throw new Error('A React form was unexpectedly submitted.')")),Yb=y('<input type="hidden"');
function Zb(a,b){this.push(Yb);if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");O(this,"name",b);O(this,"value",a);this.push($b)}
function ac(a,b,c,d,e,f,g,h){var l=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Wb(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,l=b.data):(a.push(M,"formAction",N,Xb,K),g=f=e=d=h=null,bc(b,c)));null!=h&&Q(a,"name",h);null!=d&&Q(a,"formAction",d);null!=e&&Q(a,"formEncType",e);null!=f&&Q(a,"formMethod",f);null!=g&&Q(a,"formTarget",g);return l}
function Q(a,b,c){switch(b){case "className":O(a,"class",c);break;case "tabIndex":O(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":O(a,b,c);break;case "style":Tb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(M,b,N,B(""+c),K);break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Vb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(M,"xlink:href",N,B(""+c),K);break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(M,b,N,B(c),K);break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(M,b,Ub);break;case "capture":case "download":!0===c?a.push(M,b,Ub):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(M,b,N,B(c),K);break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(M,b,N,B(c),K);break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(M,b,N,B(c),K);break;case "xlinkActuate":O(a,"xlink:actuate",c);break;case "xlinkArcrole":O(a,
"xlink:arcrole",c);break;case "xlinkRole":O(a,"xlink:role",c);break;case "xlinkShow":O(a,"xlink:show",c);break;case "xlinkTitle":O(a,"xlink:title",c);break;case "xlinkType":O(a,"xlink:type",c);break;case "xmlBase":O(a,"xml:base",c);break;case "xmlLang":O(a,"xml:lang",c);break;case "xmlSpace":O(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Ja.get(b)||b,Ha(b)){switch(typeof c){case "function":case "symbol":return;case "boolean":var d=b.toLowerCase().slice(0,
5);if("data-"!==d&&"aria-"!==d)return}a.push(M,b,N,B(c),K)}}}var R=y(">"),$b=y("/>");function cc(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function dc(a){var b="";ia.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}var ec=y(' selected=""'),fc=y('addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});');
function bc(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,fc,tb))}var gc=y("\x3c!--F!--\x3e"),hc=y("\x3c!--F--\x3e");
function ic(a,b,c,d,e,f,g){var h=b.rel,l=b.href,n=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof l||""===l)return T(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof n||null!=b.disabled||b.onLoad||b.onError)return T(a,b);f=d.styles.get(n);g=c.styleResources.hasOwnProperty(l)?c.styleResources[l]:void 0;null!==g?(c.styleResources[l]=null,f||(f={precedence:B(n),rules:[],hrefs:[],sheets:new Map},d.styles.set(n,f)),b={state:0,props:z({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&jc(b.props,g),(c=d.preloads.stylesheets.get(l))&&0<c.length?c.length=0:b.state=1),f.sheets.set(l,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(l=f.sheets.get(l))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(l);e&&a.push(Nb);return null}if(b.onLoad||b.onError)return T(a,b);e&&a.push(Nb);switch(b.rel){case "preconnect":case "dns-prefetch":return T(d.preconnectChunks,b);case "preload":return T(d.preloadChunks,b);default:return T(d.hoistableChunks,
b)}}function T(a,b){a.push(W("link"));for(var c in b)if(A.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:Q(a,c,d)}}a.push($b);return null}
function kc(a,b,c){a.push(W(c));for(var d in b)if(A.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:Q(a,d,e)}}a.push($b);return null}
function lc(a,b){a.push(W("title"));var c=null,d=null,e;for(e in b)if(A.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:Q(a,e,f)}}a.push(R);b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(B(""+b));cc(a,d,c);a.push(mc("title"));return null}
function wc(a,b){a.push(W("script"));var c=null,d=null,e;for(e in b)if(A.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:Q(a,e,f)}}a.push(R);cc(a,d,c);"string"===typeof c&&a.push(B(c));a.push(mc("script"));return null}
function xc(a,b,c){a.push(W(c));var d=c=null,e;for(e in b)if(A.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:Q(a,e,f)}}a.push(R);cc(a,d,c);return"string"===typeof c?(a.push(B(c)),null):c}var yc=y("\n"),zc=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Ac=new Map;function W(a){var b=Ac.get(a);if(void 0===b){if(!zc.test(a))throw Error("Invalid tag: "+a);b=y("<"+a);Ac.set(a,b)}return b}var Bc=y("<!DOCTYPE html>");
function Cc(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(W("select"));var h=null,l=null,n;for(n in c)if(A.call(c,n)){var q=c[n];if(null!=q)switch(n){case "children":h=q;break;case "dangerouslySetInnerHTML":l=q;break;case "defaultValue":case "value":break;default:Q(a,n,q)}}a.push(R);cc(a,l,h);return h;case "option":var r=f.selectedValue;a.push(W("option"));var p=null,v=null,x=null,U=null,t;for(t in c)if(A.call(c,
t)){var C=c[t];if(null!=C)switch(t){case "children":p=C;break;case "selected":x=C;break;case "dangerouslySetInnerHTML":U=C;break;case "value":v=C;default:Q(a,t,C)}}if(null!=r){var H=null!==v?""+v:dc(p);if(Ta(r))for(var ka=0;ka<r.length;ka++){if(""+r[ka]===H){a.push(ec);break}}else""+r===H&&a.push(ec)}else x&&a.push(ec);a.push(R);cc(a,U,p);return p;case "textarea":a.push(W("textarea"));var E=null,V=null,F=null,da;for(da in c)if(A.call(c,da)){var S=c[da];if(null!=S)switch(da){case "children":F=S;break;
case "value":E=S;break;case "defaultValue":V=S;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:Q(a,da,S)}}null===E&&null!==V&&(E=V);a.push(R);if(null!=F){if(null!=E)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ta(F)){if(1<F.length)throw Error("<textarea> can only have at most one child.");E=""+F[0]}E=""+F}"string"===typeof E&&"\n"===E[0]&&a.push(yc);null!==E&&a.push(B(""+E));return null;
case "input":a.push(W("input"));var la=null,P=null,I=null,ma=null,ya=null,qa=null,ra=null,sa=null,Ma=null,ea;for(ea in c)if(A.call(c,ea)){var aa=c[ea];if(null!=aa)switch(ea){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":la=aa;break;case "formAction":P=aa;break;case "formEncType":I=aa;break;case "formMethod":ma=aa;break;case "formTarget":ya=aa;break;case "defaultChecked":Ma=aa;
break;case "defaultValue":ra=aa;break;case "checked":sa=aa;break;case "value":qa=aa;break;default:Q(a,ea,aa)}}var od=ac(a,d,e,P,I,ma,ya,la);null!==sa?Vb(a,"checked",sa):null!==Ma&&Vb(a,"checked",Ma);null!==qa?Q(a,"value",qa):null!==ra&&Q(a,"value",ra);a.push($b);null!==od&&od.forEach(Zb,a);return null;case "button":a.push(W("button"));var bb=null,pd=null,qd=null,rd=null,sd=null,td=null,ud=null,cb;for(cb in c)if(A.call(c,cb)){var na=c[cb];if(null!=na)switch(cb){case "children":bb=na;break;case "dangerouslySetInnerHTML":pd=
na;break;case "name":qd=na;break;case "formAction":rd=na;break;case "formEncType":sd=na;break;case "formMethod":td=na;break;case "formTarget":ud=na;break;default:Q(a,cb,na)}}var vd=ac(a,d,e,rd,sd,td,ud,qd);a.push(R);null!==vd&&vd.forEach(Zb,a);cc(a,pd,bb);if("string"===typeof bb){a.push(B(bb));var wd=null}else wd=bb;return wd;case "form":a.push(W("form"));var db=null,xd=null,ta=null,eb=null,fb=null,gb=null,hb;for(hb in c)if(A.call(c,hb)){var ua=c[hb];if(null!=ua)switch(hb){case "children":db=ua;break;
case "dangerouslySetInnerHTML":xd=ua;break;case "action":ta=ua;break;case "encType":eb=ua;break;case "method":fb=ua;break;case "target":gb=ua;break;default:Q(a,hb,ua)}}var nc=null,oc=null;if("function"===typeof ta)if("function"===typeof ta.$$FORM_ACTION){var qf=Wb(d),Na=ta.$$FORM_ACTION(qf);ta=Na.action||"";eb=Na.encType;fb=Na.method;gb=Na.target;nc=Na.data;oc=Na.name}else a.push(M,"action",N,Xb,K),gb=fb=eb=ta=null,bc(d,e);null!=ta&&Q(a,"action",ta);null!=eb&&Q(a,"encType",eb);null!=fb&&Q(a,"method",
fb);null!=gb&&Q(a,"target",gb);a.push(R);null!==oc&&(a.push(Yb),O(a,"name",oc),a.push($b),null!==nc&&nc.forEach(Zb,a));cc(a,xd,db);if("string"===typeof db){a.push(B(db));var yd=null}else yd=db;return yd;case "menuitem":a.push(W("menuitem"));for(var Bb in c)if(A.call(c,Bb)){var zd=c[Bb];if(null!=zd)switch(Bb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:Q(a,Bb,zd)}}a.push(R);return null;case "title":if(3===f.insertionMode||
f.tagScope&1||null!=c.itemProp)var Ad=lc(a,c);else lc(e.hoistableChunks,c),Ad=null;return Ad;case "link":return ic(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var pc=c.async;if("string"!==typeof c.src||!c.src||!pc||"function"===typeof pc||"symbol"===typeof pc||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Bd=wc(a,c);else{var Cb=c.src;if("module"===c.type){var Db=d.moduleScriptResources;var Cd=e.preloads.moduleScripts}else Db=d.scriptResources,Cd=e.preloads.scripts;
var Eb=Db.hasOwnProperty(Cb)?Db[Cb]:void 0;if(null!==Eb){Db[Cb]=null;var qc=c;if(Eb){2===Eb.length&&(qc=z({},c),jc(qc,Eb));var Dd=Cd.get(Cb);Dd&&(Dd.length=0)}var Ed=[];e.scripts.add(Ed);wc(Ed,qc)}g&&a.push(Nb);Bd=null}return Bd;case "style":var Fb=c.precedence,za=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof Fb||"string"!==typeof za||""===za){a.push(W("style"));var Oa=null,Fd=null,ib;for(ib in c)if(A.call(c,ib)){var Gb=c[ib];if(null!=Gb)switch(ib){case "children":Oa=
Gb;break;case "dangerouslySetInnerHTML":Fd=Gb;break;default:Q(a,ib,Gb)}}a.push(R);var jb=Array.isArray(Oa)?2>Oa.length?Oa[0]:null:Oa;"function"!==typeof jb&&"symbol"!==typeof jb&&null!==jb&&void 0!==jb&&a.push(B(""+jb));cc(a,Fd,Oa);a.push(mc("style"));var Gd=null}else{var Aa=e.styles.get(Fb);if(null!==(d.styleResources.hasOwnProperty(za)?d.styleResources[za]:void 0)){d.styleResources[za]=null;Aa?Aa.hrefs.push(B(za)):(Aa={precedence:B(Fb),rules:[],hrefs:[B(za)],sheets:new Map},e.styles.set(Fb,Aa));
var Hd=Aa.rules,Pa=null,Id=null,Hb;for(Hb in c)if(A.call(c,Hb)){var rc=c[Hb];if(null!=rc)switch(Hb){case "children":Pa=rc;break;case "dangerouslySetInnerHTML":Id=rc}}var kb=Array.isArray(Pa)?2>Pa.length?Pa[0]:null:Pa;"function"!==typeof kb&&"symbol"!==typeof kb&&null!==kb&&void 0!==kb&&Hd.push(B(""+kb));cc(Hd,Id,Pa)}Aa&&e.boundaryResources&&e.boundaryResources.styles.add(Aa);g&&a.push(Nb);Gd=void 0}return Gd;case "meta":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Jd=kc(a,c,"meta");
else g&&a.push(Nb),Jd="string"===typeof c.charSet?kc(e.charsetChunks,c,"meta"):"viewport"===c.name?kc(e.preconnectChunks,c,"meta"):kc(e.hoistableChunks,c,"meta");return Jd;case "listing":case "pre":a.push(W(b));var lb=null,mb=null,nb;for(nb in c)if(A.call(c,nb)){var Ib=c[nb];if(null!=Ib)switch(nb){case "children":lb=Ib;break;case "dangerouslySetInnerHTML":mb=Ib;break;default:Q(a,nb,Ib)}}a.push(R);if(null!=mb){if(null!=lb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");
if("object"!==typeof mb||!("__html"in mb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var Ba=mb.__html;null!==Ba&&void 0!==Ba&&("string"===typeof Ba&&0<Ba.length&&"\n"===Ba[0]?a.push(yc,Ba):a.push(""+Ba))}"string"===typeof lb&&"\n"===lb[0]&&a.push(yc);return lb;case "img":var L=c.src,J=c.srcSet;if(!("lazy"===c.loading||!L&&!J||"string"!==typeof L&&null!=L||"string"!==typeof J&&
null!=J)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof L||":"!==L[4]||"d"!==L[0]&&"D"!==L[0]||"a"!==L[1]&&"A"!==L[1]||"t"!==L[2]&&"T"!==L[2]||"a"!==L[3]&&"A"!==L[3])&&("string"!==typeof J||":"!==J[4]||"d"!==J[0]&&"D"!==J[0]||"a"!==J[1]&&"A"!==J[1]||"t"!==J[2]&&"T"!==J[2]||"a"!==J[3]&&"A"!==J[3])){var Kd="string"===typeof c.sizes?c.sizes:void 0,Qa=J?J+"\n"+(Kd||""):L,sc=e.preloads.images,Ca=sc.get(Qa);if(Ca){if("high"===c.fetchPriority||10>e.highImagePreloads.size)sc.delete(Qa),
e.highImagePreloads.add(Ca)}else if(!d.imageResources.hasOwnProperty(Qa)){d.imageResources[Qa]=D;var tc=c.crossOrigin;var Ld="string"===typeof tc?"use-credentials"===tc?tc:"":void 0;var fa=e.headers,uc;fa&&0<fa.remainingCapacity&&("high"===c.fetchPriority||500>fa.highImagePreloads.length)&&(uc=Dc(L,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:Ld,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.refererPolicy}),2<=(fa.remainingCapacity-=
uc.length))?(e.resets.image[Qa]=D,fa.highImagePreloads&&(fa.highImagePreloads+=", "),fa.highImagePreloads+=uc):(Ca=[],T(Ca,{rel:"preload",as:"image",href:J?void 0:L,imageSrcSet:J,imageSizes:Kd,crossOrigin:Ld,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(Ca):(e.bulkPreloads.add(Ca),sc.set(Qa,Ca)))}}return kc(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return kc(a,
c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var Md=xc(e.headChunks,c,"head")}else Md=xc(a,c,"head");return Md;case "html":if(0===f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[Bc];var Nd=xc(e.htmlChunks,c,"html")}else Nd=xc(a,c,"html");return Nd;default:if(-1!==b.indexOf("-")){a.push(W(b));
var vc=null,Od=null,Ra;for(Ra in c)if(A.call(c,Ra)){var Da=c[Ra];if(null!=Da){var rf=Ra;switch(Ra){case "children":vc=Da;break;case "dangerouslySetInnerHTML":Od=Da;break;case "style":Tb(a,Da);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:Ha(Ra)&&"function"!==typeof Da&&"symbol"!==typeof Da&&a.push(M,rf,N,B(Da),K)}}}a.push(R);cc(a,Od,vc);return vc}}return xc(a,c,b)}var Ec=new Map;
function mc(a){var b=Ec.get(a);void 0===b&&(b=y("</"+a+">"),Ec.set(a,b));return b}function Fc(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)u(a,b[c]);return c<b.length?(c=b[c],b.length=0,w(a,c)):!0}var Gc=y('<template id="'),Hc=y('"></template>'),Ic=y("\x3c!--$--\x3e"),Jc=y('\x3c!--$?--\x3e<template id="'),Kc=y('"></template>'),Lc=y("\x3c!--$!--\x3e"),Mc=y("\x3c!--/$--\x3e"),Nc=y("<template"),Oc=y('"'),Pc=y(' data-dgst="');y(' data-msg="');y(' data-stck="');var Qc=y("></template>");
function Rc(a,b,c){u(a,Jc);if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");u(a,b.boundaryPrefix);u(a,c.toString(16));return w(a,Kc)}
var Sc=y('<div hidden id="'),Tc=y('">'),Uc=y("</div>"),Vc=y('<svg aria-hidden="true" style="display:none" id="'),Wc=y('">'),Xc=y("</svg>"),Yc=y('<math aria-hidden="true" style="display:none" id="'),Zc=y('">'),$c=y("</math>"),ad=y('<table hidden id="'),bd=y('">'),cd=y("</table>"),dd=y('<table hidden><tbody id="'),ed=y('">'),fd=y("</tbody></table>"),gd=y('<table hidden><tr id="'),hd=y('">'),id=y("</tr></table>"),jd=y('<table hidden><colgroup id="'),kd=y('">'),ld=y("</colgroup></table>");
function md(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return u(a,Sc),u(a,b.segmentPrefix),u(a,d.toString(16)),w(a,Tc);case 3:return u(a,Vc),u(a,b.segmentPrefix),u(a,d.toString(16)),w(a,Wc);case 4:return u(a,Yc),u(a,b.segmentPrefix),u(a,d.toString(16)),w(a,Zc);case 5:return u(a,ad),u(a,b.segmentPrefix),u(a,d.toString(16)),w(a,bd);case 6:return u(a,dd),u(a,b.segmentPrefix),u(a,d.toString(16)),w(a,ed);case 7:return u(a,gd),u(a,b.segmentPrefix),u(a,d.toString(16)),w(a,hd);case 8:return u(a,
jd),u(a,b.segmentPrefix),u(a,d.toString(16)),w(a,kd);default:throw Error("Unknown insertion mode. This is a bug in React.");}}function nd(a,b){switch(b.insertionMode){case 0:case 1:case 2:return w(a,Uc);case 3:return w(a,Xc);case 4:return w(a,$c);case 5:return w(a,cd);case 6:return w(a,fd);case 7:return w(a,id);case 8:return w(a,ld);default:throw Error("Unknown insertion mode. This is a bug in React.");}}
var Pd=y('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),Qd=y('$RS("'),Rd=y('","'),Sd=y('")\x3c/script>'),Td=y('<template data-rsi="" data-sid="'),Ud=y('" data-pid="'),Vd=y('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("'),
Wd=y('$RC("'),Xd=y('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Yd=y('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Zd=y('$RR("'),$d=y('","'),ae=y('",'),be=y('"'),ce=y(")\x3c/script>"),de=y('<template data-rci="" data-bid="'),ee=y('<template data-rri="" data-bid="'),fe=y('" data-sid="'),ge=y('" data-sty="'),he=y('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("'),ie=y('$RX("'),je=y('"'),ke=y(","),le=y(")\x3c/script>"),me=y('<template data-rxi="" data-bid="'),ne=y('" data-dgst="'),
oe=y('" data-msg="'),pe=y('" data-stck="'),qe=/[<\u2028\u2029]/g;function re(a){return JSON.stringify(a).replace(qe,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var se=/[&><\u2028\u2029]/g;
function te(a){return JSON.stringify(a).replace(se,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}
var ue=y('<style media="not all" data-precedence="'),ve=y('" data-href="'),we=y('">'),xe=y("</style>"),ye=!1,ze=!0;function Ae(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){u(this,ue);u(this,a.precedence);for(u(this,ve);d<c.length-1;d++)u(this,c[d]),u(this,Be);u(this,c[d]);u(this,we);for(d=0;d<b.length;d++)u(this,b[d]);ze=w(this,xe);ye=!0;b.length=0;c.length=0}}function Ce(a){return 2!==a.state?ye=!0:!1}
function De(a,b,c){ye=!1;ze=!0;b.styles.forEach(Ae,a);b.stylesheets.forEach(Ce);ye&&(c.stylesToHoist=!0);return ze}function Ee(a){for(var b=0;b<a.length;b++)u(this,a[b]);a.length=0}var Fe=[];function Ge(a){T(Fe,a.props);for(var b=0;b<Fe.length;b++)u(this,Fe[b]);Fe.length=0;a.state=2}var He=y('<style data-precedence="'),Ie=y('" data-href="'),Be=y(" "),Je=y('">'),Ke=y("</style>");
function Le(a){var b=0<a.sheets.size;a.sheets.forEach(Ge,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){u(this,He);u(this,a.precedence);a=0;if(d.length){for(u(this,Ie);a<d.length-1;a++)u(this,d[a]),u(this,Be);u(this,d[a])}u(this,Je);for(a=0;a<c.length;a++)u(this,c[a]);u(this,Ke);c.length=0;d.length=0}}
function Me(a){if(0===a.state){a.state=1;var b=a.props;T(Fe,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<Fe.length;a++)u(this,Fe[a]);Fe.length=0}}function Ne(a){a.sheets.forEach(Me,this);a.sheets.clear()}var Oe=y("["),Pe=y(",["),Qe=y(","),Re=y("]");
function Se(a,b){u(a,Oe);var c=Oe;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)u(a,c),u(a,te(""+d.props.href)),u(a,Re),c=Pe;else{u(a,c);var e=d.props["data-precedence"],f=d.props;u(a,te(""+d.props.href));e=""+e;u(a,Qe);u(a,te(e));for(var g in f)if(A.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!Ha(g))break a;h=""+h}u(e,Qe);u(e,te(l));u(e,Qe);u(e,te(h))}}}u(a,
Re);c=Pe;d.state=3}});u(a,Re)}
function Te(a,b){u(a,Oe);var c=Oe;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)u(a,c),u(a,B(JSON.stringify(""+d.props.href))),u(a,Re),c=Pe;else{u(a,c);var e=d.props["data-precedence"],f=d.props;u(a,B(JSON.stringify(""+d.props.href)));e=""+e;u(a,Qe);u(a,B(JSON.stringify(e)));for(var g in f)if(A.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:a:{e=
a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!Ha(g))break a;h=""+h}u(e,Qe);u(e,B(JSON.stringify(l)));u(e,Qe);u(e,B(JSON.stringify(h)))}}}u(a,
Re);c=Pe;d.state=3}});u(a,Re)}function Xa(a){var b=Ue();if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(Ve,We)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],T(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}Xe(b)}}}
function Ya(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(Ve,We)+">; rel=preconnect";if("string"===typeof b){var l=(""+b).replace(Ye,Ze);h+='; crossorigin="'+l+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],T(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}Xe(c)}}}
function Za(a,b,c){var d=Ue();if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var l=c.fetchPriority}var n=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(n))return;e.imageResources[n]=D;e=f.headers;var q;e&&0<e.remainingCapacity&&"high"===l&&(q=Dc(a,b,c),2<=(e.remainingCapacity-=q.length))?(f.resets.image[n]=D,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=q):(e=[],T(e,z({rel:"preload",href:g?void 0:
a,as:b},c)),"high"===l?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(n,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];T(g,z({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?D:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
T(g,z({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?D:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=D;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(n=Dc(a,b,c),2<=(e.remainingCapacity-=n.length)))f.resets.font[a]=D,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=n;else switch(e=[],
a=z({rel:"preload",href:a,as:b},c),T(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}Xe(d)}}}
function $a(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?D:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=D}T(f,z({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);Xe(c)}}}
function ab(a,b,c){var d=Ue();if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:B(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:z({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&jc(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),Xe(d))}}}
function ob(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=z({src:a,async:!0},b),f&&(2===f.length&&jc(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),wc(a,b),Xe(c))}}}
function pb(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=z({src:a,type:"module",async:!0},b),f&&(2===f.length&&jc(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),wc(a,b),Xe(c))}}}function jc(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Dc(a,b,c){a=(""+a).replace(Ve,We);b=(""+b).replace(Ye,Ze);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)A.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(Ye,Ze)+'"'));return b}var Ve=/[<>\r\n]/g;
function We(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var Ye=/["';,\r\n]/g;
function Ze(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function $e(a){this.styles.add(a)}function af(a){this.stylesheets.add(a)}
var bf=new ha.AsyncLocalStorage,cf=Symbol.for("react.element"),df=Symbol.for("react.portal"),ef=Symbol.for("react.fragment"),ff=Symbol.for("react.strict_mode"),gf=Symbol.for("react.profiler"),hf=Symbol.for("react.provider"),jf=Symbol.for("react.context"),kf=Symbol.for("react.server_context"),lf=Symbol.for("react.forward_ref"),mf=Symbol.for("react.suspense"),nf=Symbol.for("react.suspense_list"),of=Symbol.for("react.memo"),pf=Symbol.for("react.lazy"),sf=Symbol.for("react.scope"),tf=Symbol.for("react.debug_trace_mode"),
uf=Symbol.for("react.offscreen"),vf=Symbol.for("react.legacy_hidden"),wf=Symbol.for("react.cache"),xf=Symbol.for("react.default_value"),yf=Symbol.iterator;
function zf(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case ef:return"Fragment";case df:return"Portal";case gf:return"Profiler";case ff:return"StrictMode";case mf:return"Suspense";case nf:return"SuspenseList";case wf:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case jf:return(a.displayName||"Context")+".Consumer";case hf:return(a._context.displayName||"Context")+".Provider";case lf:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case of:return b=a.displayName||null,null!==b?b:zf(a.type)||"Memo";case pf:b=a._payload;a=a._init;try{return zf(a(b))}catch(c){}}return null}var Af={};function Bf(a,b){a=a.contextTypes;if(!a)return Af;var c={},d;for(d in a)c[d]=b[d];return c}var Cf=null;
function Df(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Df(a,c)}b.context._currentValue=b.value}}function Ef(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ef(a)}
function Ff(a){var b=a.parent;null!==b&&Ff(b);a.context._currentValue=a.value}function Gf(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Df(a,b):Gf(a,b)}
function Hf(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?Df(a,c):Hf(a,c);b.context._currentValue=b.value}function If(a){var b=Cf;b!==a&&(null===b?Ff(a):null===a?Ef(b):b.depth===a.depth?Df(b,a):b.depth>a.depth?Gf(b,a):Hf(b,a),Cf=a)}
var Jf={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Kf(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Jf;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:z({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Jf.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=z({},f,h)):z(f,h))}a.state=f}else f.queue=null}
var Lf={id:1,overflow:""};function Mf(a,b,c){var d=a.id;a=a.overflow;var e=32-Nf(d)-1;d&=~(1<<e);c+=1;var f=32-Nf(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Nf(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Nf=Math.clz32?Math.clz32:Of,Pf=Math.log,Qf=Math.LN2;function Of(a){a>>>=0;return 0===a?32:31-(Pf(a)/Qf|0)|0}var Rf=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Sf(){}function Tf(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Sf,Sf),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Uf=b;throw Rf;}}var Uf=null;
function Vf(){if(null===Uf)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Uf;Uf=null;return a}function Wf(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Xf="function"===typeof Object.is?Object.is:Wf,Yf=null,Zf=null,$f=null,ag=null,bg=null,X=null,cg=!1,dg=!1,eg=0,fg=0,gg=-1,hg=0,ig=null,jg=null,kg=0;
function lg(){if(null===Yf)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return Yf}
function mg(){if(0<kg)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function ng(){null===X?null===bg?(cg=!1,bg=X=mg()):(cg=!0,X=bg):null===X.next?(cg=!1,X=X.next=mg()):(cg=!0,X=X.next);return X}function og(a,b,c,d){for(;dg;)dg=!1,fg=eg=0,gg=-1,hg=0,kg+=1,X=null,c=a(b,d);pg();return c}function qg(){var a=ig;ig=null;return a}function pg(){ag=$f=Zf=Yf=null;dg=!1;bg=null;kg=0;X=jg=null}
function rg(a,b){return"function"===typeof b?b(a):b}function sg(a,b,c){Yf=lg();X=ng();if(cg){var d=X.queue;b=d.dispatch;if(null!==jg&&(c=jg.get(d),void 0!==c)){jg.delete(d);d=X.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);X.memoizedState=d;return[d,b]}return[X.memoizedState,b]}a=a===rg?"function"===typeof b?b():b:void 0!==c?c(b):b;X.memoizedState=a;a=X.queue={last:null,dispatch:null};a=a.dispatch=tg.bind(null,Yf,a);return[X.memoizedState,a]}
function ug(a,b){Yf=lg();X=ng();b=void 0===b?null:b;if(null!==X){var c=X.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Xf(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();X.memoizedState=[a,b];return a}
function tg(a,b,c){if(25<=kg)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===Yf)if(dg=!0,a={action:c,next:null},null===jg&&(jg=new Map),c=jg.get(b),void 0===c)jg.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function vg(){throw Error("startTransition cannot be called during server rendering.");}function wg(){throw Error("Cannot update optimistic state while rendering.");}
function xg(a,b,c){if(void 0!==a)return"p"+a;a=JSON.stringify([b,null,c]);b=ca.createHash("md5");b.update(a);return"k"+b.digest("hex")}function yg(a){var b=hg;hg+=1;null===ig&&(ig=[]);return Tf(ig,a,b)}function zg(){throw Error("Cache cannot be refreshed during server rendering.");}function Ag(){}
var Cg={readContext:function(a){return a._currentValue},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return yg(a);if(a.$$typeof===jf||a.$$typeof===kf)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){lg();return a._currentValue},useMemo:ug,useReducer:sg,useRef:function(a){Yf=lg();X=ng();var b=X.memoizedState;return null===b?(a={current:a},X.memoizedState=a):b},useState:function(a){return sg(rg,a)},
useInsertionEffect:Ag,useLayoutEffect:Ag,useCallback:function(a,b){return ug(function(){return a},b)},useImperativeHandle:Ag,useEffect:Ag,useDebugValue:Ag,useDeferredValue:function(a){lg();return a},useTransition:function(){lg();return[!1,vg]},useId:function(){var a=Zf.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Nf(a)-1)).toString(32)+b;var c=Bg;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=eg++;a=":"+c.idPrefix+"R"+a;0<b&&
(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return zg},useHostTransitionStatus:function(){lg();return Va},useOptimistic:function(a){lg();return[a,wg]},useFormState:function(a,b,c){lg();var d=fg++,e=$f;if("function"===typeof a.$$FORM_ACTION){var f=null,g=ag;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;
if(null!==e&&"function"===typeof h){var l=e[1];h.call(a,e[2],e[3])&&(f=xg(c,g,d),l===f&&(gg=d,b=e[0]))}var n=a.bind(null,b);a=function(r){n(r)};"function"===typeof n.$$FORM_ACTION&&(a.$$FORM_ACTION=function(r){r=n.$$FORM_ACTION(r);void 0!==c&&(c+="",r.action=c);var p=r.data;p&&(null===f&&(f=xg(c,g,d)),p.append("$ACTION_KEY",f));return r});return[b,a]}var q=a.bind(null,b);return[b,function(r){q(r)}]}},Bg=null,Dg={getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");
}},Eg=Ua.ReactCurrentDispatcher,Fg=Ua.ReactCurrentCache;function Gg(a){console.error(a);return null}function Hg(){}var Ig=null;function Ue(){if(Ig)return Ig;var a=bf.getStore();return a?a:null}function Jg(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return Kg(a)}))}
function Lg(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function Mg(a,b,c,d,e,f,g,h,l,n,q,r){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var p={replay:null,node:c,childIndex:d,ping:function(){return Jg(a,p)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:l,legacyContext:n,context:q,treeContext:r,thenableState:b};g.add(p);return p}
function Ng(a,b,c,d,e,f,g,h,l,n,q,r){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var p={replay:c,node:d,childIndex:e,ping:function(){return Jg(a,p)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:l,legacyContext:n,context:q,treeContext:r,thenableState:b};g.add(p);return p}function Og(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function Y(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function Pg(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function Qg(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((zf(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=z({},c,d)}b.legacyContext=e;Z(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Z(a,b,null,f,-1),b.keyPath=e}
function Rg(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var l=b.blockedSegment;if(null!==l){h=!0;l=l.chunks;for(var n=0;n<f;n++)n===g?l.push(gc):l.push(hc)}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Mf(c,1,0),Sg(a,b,d,-1),b.treeContext=c):h?Sg(a,b,d,-1):Z(a,b,null,d,-1);b.keyPath=f}function Tg(a,b){if(a&&a.defaultProps){b=z({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function Ug(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=Bf(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue:d);Kf(h,e,f,d);Qg(a,b,c,h,e)}else{h=Bf(e,b.legacyContext);Yf={};Zf=b;$f=a;ag=c;fg=eg=0;gg=-1;hg=0;ig=d;d=e(f,h);d=og(e,f,d,h);g=0!==eg;var l=fg,n=gg;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(Kf(d,e,f,h),Qg(a,b,c,d,e)):Rg(a,b,c,d,g,l,n)}else if("string"===typeof e)if(d=
b.blockedSegment,null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=Mb(h,e,f),b.keyPath=c,Sg(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=Cc(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;l=b.keyPath;b.formatContext=Mb(h,e,f);b.keyPath=c;Sg(a,b,g,-1);b.formatContext=h;b.keyPath=l;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push(mc(e))}d.lastPushedText=!1}else{switch(e){case vf:case tf:case ff:case gf:case ef:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case uf:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Z(a,b,null,f.children,-1),b.keyPath=e);return;case nf:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case sf:throw Error("ReactDOMServer does not yet support scope components.");
case mf:a:if(null!==b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{Sg(a,b,c,-1)}finally{b.keyPath=e}}else{n=b.keyPath;e=b.blockedBoundary;var q=b.blockedSegment;d=f.fallback;var r=f.children;f=new Set;g=Lg(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);l=Og(a,q.chunks.length,g,b.formatContext,!1,!1);q.children.push(l);q.lastPushedText=!1;var p=Og(a,0,null,b.formatContext,!1,!1);p.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=p;a.renderState.boundaryResources=g.resources;b.keyPath=
c;try{if(Sg(a,b,r,-1),p.lastPushedText&&p.textEmbedded&&p.chunks.push(Nb),p.status=1,Vg(g,p),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(v){p.status=4,g.status=4,h=Y(a,v),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=q,b.keyPath=n}h=[c[0],"Suspense Fallback",c[2]];n=a.trackedPostpones;null!==n&&(q=[h[1],h[2],[],null],n.workingMap.set(h,q),5===g.status?n.workingMap.get(c)[4]=q:g.trackedFallbackNode=q);b=Mg(a,null,d,-1,
e,l,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case lf:e=e.render;Yf={};Zf=b;$f=a;ag=c;fg=eg=0;gg=-1;hg=0;ig=d;d=e(f,g);f=og(e,f,d,g);Rg(a,b,c,f,0!==eg,fg,gg);return;case of:e=e.type;f=Tg(e,f);Ug(a,b,c,d,e,f,g);return;case hf:h=f.children;d=b.keyPath;e=e._context;f=f.value;g=e._currentValue;e._currentValue=f;l=Cf;Cf=f={parent:l,depth:null===l?0:l.depth+1,context:e,parentValue:g,value:f};b.context=f;
b.keyPath=c;Z(a,b,null,h,-1);a=Cf;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===xf?a.context._defaultValue:c;a=Cf=a.parent;b.context=a;b.keyPath=d;return;case jf:f=f.children;f=f(e._currentValue);e=b.keyPath;b.keyPath=c;Z(a,b,null,f,-1);b.keyPath=e;return;case pf:h=e._init;e=h(e._payload);f=Tg(e,f);Ug(a,b,c,d,e,f,void 0);return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+
((null==e?e:typeof e)+"."));}}function Wg(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=Og(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Sg(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(Vg(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Z(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)Wg(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case cf:var f=d.type,g=d.key,h=d.props,l=d.ref,n=zf(f),q=null==g?-1===e?0:e:g;g=[b.keyPath,n,q];if(null!==b.replay)a:{var r=b.replay;e=r.nodes;for(d=0;d<e.length;d++){var p=e[d];if(q===p[1]){if(4===p.length){if(null!==n&&n!==p[0])throw Error("Expected the resume to render <"+p[0]+"> in this slot but instead it rendered <"+
n+">. The tree doesn't match so React will fallback to client rendering.");n=p[2];p=p[3];q=b.node;b.replay={nodes:n,slots:p,pendingTasks:1};try{Ug(a,b,g,c,f,h,l);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(H){if("object"===typeof H&&null!==H&&(H===Rf||"function"===typeof H.then))throw b.node===q&&(b.replay=r),H;b.replay.pendingTasks--;
g=a;a=b.blockedBoundary;c=H;h=Y(g,c);Xg(g,a,n,p,c,h)}b.replay=r}else{if(f!==mf)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(zf(f)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{r=void 0;c=p[5];f=p[2];l=p[3];n=null===p[4]?[]:p[4][2];p=null===p[4]?null:p[4][3];q=b.keyPath;var v=b.replay,x=b.blockedBoundary,U=h.children;h=h.fallback;var t=new Set,C=Lg(a,t);C.parentFlushed=!0;C.rootSegmentID=c;b.blockedBoundary=
C;b.replay={nodes:f,slots:l,pendingTasks:1};a.renderState.boundaryResources=C.resources;try{Sg(a,b,U,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===C.pendingTasks&&0===C.status){C.status=1;a.completedBoundaries.push(C);break b}}catch(H){C.status=4,r=Y(a,H),C.errorDigest=r,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(C)}finally{a.renderState.boundaryResources=
x?x.resources:null,b.blockedBoundary=x,b.replay=v,b.keyPath=q}b=Ng(a,null,{nodes:n,slots:p,pendingTasks:0},h,-1,x,t,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else Ug(a,b,g,c,f,h,l);return;case df:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");case pf:h=d._init;d=h(d._payload);Z(a,b,null,d,e);return}if(Ta(d)){Yg(a,
b,d,e);return}null===d||"object"!==typeof d?h=null:(h=yf&&d[yf]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);Yg(a,b,g,e)}return}if("function"===typeof d.then)return Z(a,b,null,yg(d),e);if(d.$$typeof===jf||d.$$typeof===kf)return Z(a,b,null,d._currentValue,e);e=Object.prototype.toString.call(d);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+
"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=Ob(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=Ob(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function Yg(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var l=g[h];if(l[1]===d){d=l[2];l=l[3];b.replay={nodes:d,slots:l,pendingTasks:1};try{Yg(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(r){if("object"===typeof r&&
null!==r&&(r===Rf||"function"===typeof r.then))throw r;b.replay.pendingTasks--;c=a;var n=b.blockedBoundary,q=r;a=Y(c,q);Xg(c,n,d,l,q,a)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)l=c[d],b.treeContext=Mf(f,g,d),n=h[d],"number"===typeof n?(Wg(a,b,n,l,d),delete h[d]):Sg(a,b,l,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Mf(f,g,h),Sg(a,b,d,h);b.treeContext=
f;b.keyPath=e}
function Sg(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,l=b.treeContext,n=b.blockedSegment;if(null===n)try{return Z(a,b,null,c,d)}catch(p){if(pg(),c=p===Rf?Vf():p,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=qg();a=Ng(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;If(g);return}}else{var q=
n.children.length,r=n.chunks.length;try{return Z(a,b,null,c,d)}catch(p){if(pg(),n.children.length=q,n.chunks.length=r,c=p===Rf?Vf():p,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=qg();n=b.blockedSegment;q=Og(a,n.chunks.length,null,b.formatContext,n.lastPushedText,!0);n.children.push(q);n.lastPushedText=!1;a=Mg(a,d,b.node,b.childIndex,b.blockedBoundary,q,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=l;If(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;If(g);throw c;}function Zg(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,$g(this,b,a))}
function Xg(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)Xg(a,b,h[2],h[3],e,f);else{h=h[5];var l=a,n=f,q=Lg(l,new Set);q.parentFlushed=!0;q.rootSegmentID=h;q.status=4;q.errorDigest=n;q.parentFlushed&&l.clientRenderedBoundaries.push(q)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var r in d)delete d[r]}}
function ah(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(1!==b.status&&2!==b.status){a=a.replay;if(null===a){Y(b,c);Pg(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=Y(b,c),Xg(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&bh(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=Y(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return ah(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&ch(b)}
function dh(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var l=h.value.sheets.values(),n=l.next();0<e.remainingCapacity&&!n.done;n=l.next()){var q=n.value,r=q.props,p=r.href,v=q.props,x=Dc(v.href,"style",{crossOrigin:v.crossOrigin,integrity:v.integrity,
nonce:v.nonce,type:v.type,fetchPriority:v.fetchPriority,referrerPolicy:v.referrerPolicy,media:v.media});if(2<=(e.remainingCapacity-=x.length))c.resets.style[p]=D,f&&(f+=", "),f+=x,c.resets.style[p]="string"===typeof r.crossOrigin||"string"===typeof r.integrity?[r.crossOrigin,r.integrity]:D;else break b}}f?d({Link:f}):d({})}}}catch(U){Y(a,U)}}function bh(a){null===a.trackedPostpones&&dh(a,!0);a.onShellError=Hg;a=a.onShellReady;a()}
function ch(a){dh(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function Vg(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&Vg(a,c)}else a.completedSegments.push(b)}
function $g(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&bh(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&Vg(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(Zg,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(Vg(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&ch(a)}
function Kg(a){if(2!==a.status){var b=Cf,c=Eg.current;Eg.current=Cg;var d=Fg.current;Fg.current=Dg;var e=Ig;Ig=a;var f=Bg;Bg=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var l=g[h],n=a,q=l.blockedBoundary;n.renderState.boundaryResources=q?q.resources:null;var r=l.blockedSegment;if(null===r){var p=n;if(0!==l.replay.pendingTasks){If(l.context);try{var v=l.thenableState;l.thenableState=null;Z(p,l,v,l.node,l.childIndex);if(1===l.replay.pendingTasks&&0<l.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
l.replay.pendingTasks--;l.abortSet.delete(l);$g(p,l.blockedBoundary,null)}catch(I){pg();var x=I===Rf?Vf():I;if("object"===typeof x&&null!==x&&"function"===typeof x.then){var U=l.ping;x.then(U,U);l.thenableState=qg()}else{l.replay.pendingTasks--;l.abortSet.delete(l);n=void 0;var t=p,C=l.blockedBoundary,H=x,ka=l.replay.nodes,E=l.replay.slots;n=Y(t,H);Xg(t,C,ka,E,H,n);p.pendingRootTasks--;0===p.pendingRootTasks&&bh(p);p.allPendingTasks--;0===p.allPendingTasks&&ch(p)}}finally{p.renderState.boundaryResources=
null}}}else if(p=void 0,t=r,0===t.status){If(l.context);var V=t.children.length,F=t.chunks.length;try{var da=l.thenableState;l.thenableState=null;Z(n,l,da,l.node,l.childIndex);t.lastPushedText&&t.textEmbedded&&t.chunks.push(Nb);l.abortSet.delete(l);t.status=1;$g(n,l.blockedBoundary,t)}catch(I){pg();t.children.length=V;t.chunks.length=F;var S=I===Rf?Vf():I;if("object"===typeof S&&null!==S&&"function"===typeof S.then){var la=l.ping;S.then(la,la);l.thenableState=qg()}else{l.abortSet.delete(l);t.status=
4;var P=l.blockedBoundary;p=Y(n,S);null===P?Pg(n,S):(P.pendingTasks--,4!==P.status&&(P.status=4,P.errorDigest=p,P.parentFlushed&&n.clientRenderedBoundaries.push(P)));n.allPendingTasks--;0===n.allPendingTasks&&ch(n)}}finally{n.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&eh(a,a.destination)}catch(I){Y(a,I),Pg(a,I)}finally{Bg=f,Eg.current=c,Fg.current=d,c===Cg&&If(b),Ig=e}}}
function fh(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;u(b,Gc);u(b,a.placeholderPrefix);a=d.toString(16);u(b,a);return w(b,Hc);case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)u(b,d[f]);e=gh(a,b,e)}for(;f<d.length-1;f++)u(b,d[f]);f<d.length&&(e=w(b,d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function gh(a,b,c){var d=c.boundary;if(null===d)return fh(a,b,c);d.parentFlushed=!0;if(4===d.status)d=d.errorDigest,w(b,Lc),u(b,Nc),d&&(u(b,Pc),u(b,B(d)),u(b,Oc)),w(b,Qc),fh(a,b,c);else if(1!==d.status)0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Rc(b,a.renderState,d.rootSegmentID),fh(a,b,c);else if(d.byteSize>a.progressiveChunkSize)d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Rc(b,a.renderState,d.rootSegmentID),fh(a,b,
c);else{c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach($e,e),c.stylesheets.forEach(af,e));w(b,Ic);d=d.completedSegments;if(1!==d.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");gh(a,b,d[0])}return w(b,Mc)}function hh(a,b,c){md(b,a.renderState,c.parentFormatContext,c.id);gh(a,b,c);return nd(b,c.parentFormatContext)}
function ih(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)jh(a,b,c,d[e]);d.length=0;De(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(u(b,a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,u(b,2048<Xd.length?Xd.slice():Xd)):0===(d.instructions&8)?(d.instructions|=8,u(b,Yd)):u(b,Zd):0===(d.instructions&2)?(d.instructions|=
2,u(b,Vd)):u(b,Wd)):f?u(b,ee):u(b,de);d=e.toString(16);u(b,a.boundaryPrefix);u(b,d);g?u(b,$d):u(b,fe);u(b,a.segmentPrefix);u(b,d);f?g?(u(b,ae),Se(b,c)):(u(b,ge),Te(b,c)):g&&u(b,be);d=g?w(b,ce):w(b,rb);return Fc(b,a)&&d}
function jh(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return hh(a,b,d)}if(e===c.rootSegmentID)return hh(a,b,d);hh(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(u(b,a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,u(b,Pd)):u(b,Qd)):u(b,Td);u(b,a.segmentPrefix);e=e.toString(16);u(b,e);d?u(b,Rd):u(b,Ud);u(b,a.placeholderPrefix);u(b,
e);b=d?w(b,Sd):w(b,rb);return b}
function eh(a,b){k=new Uint8Array(2048);m=0;pa=!0;try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,l=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(l))}var n=e.htmlChunks,q=e.headChunks;f=0;if(n){for(f=0;f<n.length;f++)u(b,n[f]);if(q)for(f=0;f<q.length;f++)u(b,q[f]);
else u(b,W("head")),u(b,R)}else if(q)for(f=0;f<q.length;f++)u(b,q[f]);var r=e.charsetChunks;for(f=0;f<r.length;f++)u(b,r[f]);r.length=0;e.preconnects.forEach(Ee,b);e.preconnects.clear();var p=e.preconnectChunks;for(f=0;f<p.length;f++)u(b,p[f]);p.length=0;e.fontPreloads.forEach(Ee,b);e.fontPreloads.clear();e.highImagePreloads.forEach(Ee,b);e.highImagePreloads.clear();e.styles.forEach(Le,b);var v=e.importMapChunks;for(f=0;f<v.length;f++)u(b,v[f]);v.length=0;e.bootstrapScripts.forEach(Ee,b);e.scripts.forEach(Ee,
b);e.scripts.clear();e.bulkPreloads.forEach(Ee,b);e.bulkPreloads.clear();var x=e.preloadChunks;for(f=0;f<x.length;f++)u(b,x[f]);x.length=0;var U=e.hoistableChunks;for(f=0;f<U.length;f++)u(b,U[f]);U.length=0;n&&null===q&&u(b,mc("head"));gh(a,b,d);a.completedRootSegment=null;Fc(b,a.renderState)}else return;var t=a.renderState;d=0;t.preconnects.forEach(Ee,b);t.preconnects.clear();var C=t.preconnectChunks;for(d=0;d<C.length;d++)u(b,C[d]);C.length=0;t.fontPreloads.forEach(Ee,b);t.fontPreloads.clear();
t.highImagePreloads.forEach(Ee,b);t.highImagePreloads.clear();t.styles.forEach(Ne,b);t.scripts.forEach(Ee,b);t.scripts.clear();t.bulkPreloads.forEach(Ee,b);t.bulkPreloads.clear();var H=t.preloadChunks;for(d=0;d<H.length;d++)u(b,H[d]);H.length=0;var ka=t.hoistableChunks;for(d=0;d<ka.length;d++)u(b,ka[d]);ka.length=0;var E=a.clientRenderedBoundaries;for(c=0;c<E.length;c++){var V=E[c];t=b;var F=a.resumableState,da=a.renderState,S=V.rootSegmentID,la=V.errorDigest,P=V.errorMessage,I=V.errorComponentStack,
ma=0===F.streamingFormat;ma?(u(t,da.startInlineScript),0===(F.instructions&4)?(F.instructions|=4,u(t,he)):u(t,ie)):u(t,me);u(t,da.boundaryPrefix);u(t,S.toString(16));ma&&u(t,je);if(la||P||I)ma?(u(t,ke),u(t,re(la||""))):(u(t,ne),u(t,B(la||"")));if(P||I)ma?(u(t,ke),u(t,re(P||""))):(u(t,oe),u(t,B(P||"")));I&&(ma?(u(t,ke),u(t,re(I))):(u(t,pe),u(t,B(I))));if(ma?!w(t,le):!w(t,rb)){a.destination=null;c++;E.splice(0,c);return}}E.splice(0,c);var ya=a.completedBoundaries;for(c=0;c<ya.length;c++)if(!ih(a,b,
ya[c])){a.destination=null;c++;ya.splice(0,c);return}ya.splice(0,c);xa(b);k=new Uint8Array(2048);m=0;pa=!0;var qa=a.partialBoundaries;for(c=0;c<qa.length;c++){var ra=qa[c];a:{E=a;V=b;E.renderState.boundaryResources=ra.resources;var sa=ra.completedSegments;for(F=0;F<sa.length;F++)if(!jh(E,V,ra,sa[F])){F++;sa.splice(0,F);var Ma=!1;break a}sa.splice(0,F);Ma=De(V,ra.resources,E.renderState)}if(!Ma){a.destination=null;c++;qa.splice(0,c);return}}qa.splice(0,c);var ea=a.completedBoundaries;for(c=0;c<ea.length;c++)if(!ih(a,
b,ea[c])){a.destination=null;c++;ea.splice(0,c);return}ea.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length?(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&u(b,mc("body")),c.hasHtml&&u(b,mc("html")),xa(b),oa(b),b.end(),a.destination=null):(xa(b),oa(b))}}
function kh(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return bf.run(a,Kg,a)});null===a.trackedPostpones&&setImmediate(function(){return bf.run(a,lh,a)})}function lh(a){dh(a,0===a.pendingRootTasks)}function Xe(a){!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(a.flushScheduled=!0,setImmediate(function(){var b=a.destination;b?eh(a,b):a.flushScheduled=!1}))}
function mh(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{eh(a,b)}catch(c){Y(a,c),Pg(a,c)}}}function nh(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return ah(e,a,d)});c.clear()}null!==a.destination&&eh(a,a.destination)}catch(e){Y(a,e),Pg(a,e)}}function oh(a,b){return function(){return mh(b,a)}}
function ph(a,b){return function(){a.destination=null;nh(a,Error(b))}}
function qh(a,b){var c=b?b.identifierPrefix:void 0;var d=0;void 0!==(b?b.unstable_externalRuntimeSrc:void 0)&&(d=1);c={idPrefix:void 0===c?"":c,nextFormID:0,streamingFormat:d,bootstrapScriptContent:b?b.bootstrapScriptContent:void 0,bootstrapScripts:b?b.bootstrapScripts:void 0,bootstrapModules:b?b.bootstrapModules:void 0,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},
moduleUnknownResources:{},moduleScriptResources:{}};var e=b?b.nonce:void 0,f=b?b.unstable_externalRuntimeSrc:void 0,g=b?b.importMap:void 0;d=b?b.onHeaders:void 0;var h=b?b.maxHeadersLength:void 0,l=void 0===e?sb:y('<script nonce="'+B(e)+'">'),n=c.idPrefix,q=[],r=null,p=c.bootstrapScriptContent,v=c.bootstrapScripts,x=c.bootstrapModules;void 0!==p&&q.push(l,(""+p).replace(Ab,Jb),tb);void 0!==f&&("string"===typeof f?(r={src:f,chunks:[]},wc(r.chunks,{src:f,async:!0,integrity:void 0,nonce:e})):(r={src:f.src,
chunks:[]},wc(r.chunks,{src:f.src,async:!0,integrity:f.integrity,nonce:e})));f=[];void 0!==g&&(f.push(Kb),f.push((""+JSON.stringify(g)).replace(Ab,Jb)),f.push(Lb));g=d?{preconnects:"",fontPreloads:"",highImagePreloads:"",remainingCapacity:"number"===typeof h?h:2E3}:null;d={placeholderPrefix:y(n+"P:"),segmentPrefix:y(n+"S:"),boundaryPrefix:y(n+"B:"),startInlineScript:l,htmlChunks:null,headChunks:null,externalRuntimeScript:r,bootstrapChunks:q,onHeaders:d,headers:g,resets:{font:{},dns:{},connect:{default:{},
anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],preconnectChunks:[],importMapChunks:f,preloadChunks:[],hoistableChunks:[],preconnects:new Set,fontPreloads:new Set,highImagePreloads:new Set,styles:new Map,bootstrapScripts:new Set,scripts:new Set,bulkPreloads:new Set,preloads:{images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map},nonce:e,boundaryResources:null,stylesToHoist:!1};if(void 0!==v)for(l=0;l<v.length;l++)f=v[l],g=r=void 0,h={rel:"preload",as:"script",fetchPriority:"low",
nonce:e},"string"===typeof f?h.href=n=f:(h.href=n=f.src,h.integrity=g="string"===typeof f.integrity?f.integrity:void 0,h.crossOrigin=r="string"===typeof f||null==f.crossOrigin?void 0:"use-credentials"===f.crossOrigin?"use-credentials":""),f=c,p=n,f.scriptResources[p]=null,f.moduleScriptResources[p]=null,f=[],T(f,h),d.bootstrapScripts.add(f),q.push(ub,B(n)),e&&q.push(wb,B(e)),"string"===typeof g&&q.push(xb,B(g)),"string"===typeof r&&q.push(yb,B(r)),q.push(zb);if(void 0!==x)for(v=0;v<x.length;v++)h=
x[v],r=n=void 0,g={rel:"modulepreload",fetchPriority:"low",nonce:e},"string"===typeof h?g.href=l=h:(g.href=l=h.src,g.integrity=r="string"===typeof h.integrity?h.integrity:void 0,g.crossOrigin=n="string"===typeof h||null==h.crossOrigin?void 0:"use-credentials"===h.crossOrigin?"use-credentials":""),h=c,f=l,h.scriptResources[f]=null,h.moduleScriptResources[f]=null,h=[],T(h,g),d.bootstrapScripts.add(h),q.push(vb,B(l)),e&&q.push(wb,B(e)),"string"===typeof r&&q.push(xb,B(r)),"string"===typeof n&&q.push(yb,
B(n)),q.push(zb);e=b?b.namespaceURI:void 0;e=G("http://www.w3.org/2000/svg"===e?3:"http://www.w3.org/1998/Math/MathML"===e?4:0,null,0);x=b?b.progressiveChunkSize:void 0;v=b?b.onError:void 0;l=b?b.onAllReady:void 0;n=b?b.onShellReady:void 0;r=b?b.onShellError:void 0;g=b?b.onPostpone:void 0;h=b?b.formState:void 0;Wa.current=qb;b=[];q=new Set;c={destination:null,flushScheduled:!1,resumableState:c,renderState:d,rootFormatContext:e,progressiveChunkSize:void 0===x?12800:x,status:0,fatalError:null,nextSegmentId:0,
allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:q,pingedTasks:b,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===v?Gg:v,onPostpone:void 0===g?Hg:g,onAllReady:void 0===l?Hg:l,onShellReady:void 0===n?Hg:n,onShellError:void 0===r?Hg:r,onFatalError:Hg,formState:void 0===h?null:h};d=Og(c,0,null,e,!1,!1);d.parentFlushed=!0;a=Mg(c,null,a,-1,null,d,q,null,e,Af,null,Lf);b.push(a);return c}
exports.renderToPipeableStream=function(a,b){var c=qh(a,b),d=!1;kh(c);return{pipe:function(e){if(d)throw Error("React currently only supports piping to one writable stream.");d=!0;dh(c,null===c.trackedPostpones?0===c.pendingRootTasks:null===c.completedRootSegment?0===c.pendingRootTasks:5!==c.completedRootSegment.status);mh(c,e);e.on("drain",oh(e,c));e.on("error",ph(c,"The destination stream errored while writing data."));e.on("close",ph(c,"The destination stream closed early."));return e},abort:function(e){nh(c,
e)}}};exports.version="18.3.0-canary-2c338b16f-20231116";

//# sourceMappingURL=react-dom-server.node.production.min.js.map
