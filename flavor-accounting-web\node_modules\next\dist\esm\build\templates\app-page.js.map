{"version": 3, "sources": ["../../../src/build/templates/app-page.ts"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "originalPathname", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": "AAEE;AACF,SAASA,kBAAkB,QAAQ,6DAA4D;AAC/F,SAASC,SAAS,QAAQ,iCAAgC;AAW1D,yEAAyE;AACzE,UAAU;AACV,cAAc;AACd,eAAe;AAEf,SAASC,IAAI,EAAEC,KAAK,GAAE;AAEtB,SAASC,WAAWC,WAAW,QAAQ,0BAAyB;AAMhE,8BAA8B;AAC9B,iCAAiC;AAEjC,OAAO,MAAMC,mBAAmB,wBAAuB;AACvD,OAAO,MAAMC,eAAe;IAC1BC,SAASC;IACTC,WAAWC;AACb,EAAC;AAED,cAAc,qCAAoC;AAElD,4DAA4D;AAC5D,OAAO,MAAMC,cAAc,IAAIZ,mBAAmB;IAChDa,YAAY;QACVC,MAAMb,UAAUc,QAAQ;QACxBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;QACVC,UAAU,EAAE;IACd;IACAC,UAAU;QACRC,YAAYpB;IACd;AACF,GAAE"}