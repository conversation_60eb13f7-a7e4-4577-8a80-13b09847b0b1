/*
 React
 react-server-dom-webpack-client.node.unbundled.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var p=require("util"),r=require("react-dom"),u=require("react"),v={stream:!0};function w(a,b){var d=a[b[0]];if(a=d[b[2]])d=a.name;else{a=d["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');d=b[2]}return{specifier:a.specifier,name:d,async:4===b.length}}var x=new Map;
function y(a){var b=x.get(a.specifier);if(b)return"fulfilled"===b.status?null:b;var d=import(a.specifier);a.async&&(d=d.then(function(c){return c.default}));d.then(function(c){var g=d;g.status="fulfilled";g.value=c},function(c){var g=d;g.status="rejected";g.reason=c});x.set(a.specifier,d);return d}
function z(a,b,d){if(null!==a)for(var c=1;c<b.length;c+=2){var g=d,h=A.current;if(h){var l=h.preinitScript,k=a.prefix+b[c];var e=a.crossOrigin;e="string"===typeof e?"use-credentials"===e?e:"":void 0;l.call(h,k,{crossOrigin:e,nonce:g})}}}var A=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,B=Symbol.for("react.element"),C=Symbol.for("react.provider"),D=Symbol.for("react.server_context"),F=Symbol.for("react.lazy"),G=Symbol.for("react.default_value"),H=Symbol.iterator;
function aa(a){if(null===a||"object"!==typeof a)return null;a=H&&a[H]||a["@@iterator"];return"function"===typeof a?a:null}var ba=Array.isArray,I=Object.getPrototypeOf,ca=Object.prototype,J=new WeakMap;function da(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function ea(a,b,d,c){function g(e,f){if(null===f)return null;if("object"===typeof f){if("function"===typeof f.then){null===k&&(k=new FormData);l++;var t=h++;f.then(function(n){n=JSON.stringify(n,g);var q=k;q.append(b+t,n);l--;0===l&&d(q)},function(n){c(n)});return"$@"+t.toString(16)}if(ba(f))return f;if(f instanceof FormData){null===k&&(k=new FormData);var E=k;e=h++;var m=b+e+"_";f.forEach(function(n,q){E.append(m+q,n)});return"$K"+e.toString(16)}if(f instanceof Map)return f=JSON.stringify(Array.from(f),
g),null===k&&(k=new FormData),e=h++,k.append(b+e,f),"$Q"+e.toString(16);if(f instanceof Set)return f=JSON.stringify(Array.from(f),g),null===k&&(k=new FormData),e=h++,k.append(b+e,f),"$W"+e.toString(16);if(aa(f))return Array.from(f);e=I(f);if(e!==ca&&(null===e||null!==I(e)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return f}if("string"===typeof f){if("Z"===f[f.length-1]&&this[e]instanceof Date)return"$D"+f;
f="$"===f[0]?"$"+f:f;return f}if("boolean"===typeof f)return f;if("number"===typeof f)return da(f);if("undefined"===typeof f)return"$undefined";if("function"===typeof f){f=J.get(f);if(void 0!==f)return f=JSON.stringify(f,g),null===k&&(k=new FormData),e=h++,k.set(b+e,f),"$F"+e.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof f){e=f.description;if(Symbol.for(e)!==f)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+
(f.description+") cannot be found among global symbols."));return"$S"+e}if("bigint"===typeof f)return"$n"+f.toString(10);throw Error("Type "+typeof f+" is not supported as an argument to a Server Function.");}var h=1,l=0,k=null;a=JSON.stringify(a,g);null===k?d(a):(k.set(b+"0",a),0===l&&d(k))}var K=new WeakMap;
function fa(a){var b,d,c=new Promise(function(g,h){b=g;d=h});ea(a,"",function(g){if("string"===typeof g){var h=new FormData;h.append("0",g);g=h}c.status="fulfilled";c.value=g;b(g)},function(g){c.status="rejected";c.reason=g;d(g)});return c}
function ha(a){var b=J.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var d=null;if(null!==b.bound){d=K.get(b);d||(d=fa(b),K.set(b,d));if("rejected"===d.status)throw d.reason;if("fulfilled"!==d.status)throw d;b=d.value;var c=new FormData;b.forEach(function(g,h){c.append("$ACTION_"+a+":"+h,g)});d=c;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:d}}
function ia(a,b){var d=J.get(this);if(!d)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(d.id!==a)return!1;var c=d.bound;if(null===c)return 0===b;switch(c.status){case "fulfilled":return c.value.length===b;case "pending":throw c;case "rejected":throw c.reason;default:throw"string"!==typeof c.status&&(c.status="pending",c.then(function(g){c.status="fulfilled";c.value=g},function(g){c.status="rejected";c.reason=g})),c;}}
function L(a,b){Object.defineProperties(a,{$$FORM_ACTION:{value:ha},$$IS_SIGNATURE_EQUAL:{value:ia},bind:{value:ja}});J.set(a,b)}var ka=Function.prototype.bind,la=Array.prototype.slice;function ja(){var a=ka.apply(this,arguments),b=J.get(this);if(b){var d=la.call(arguments,1),c=null;c=null!==b.bound?Promise.resolve(b.bound).then(function(g){return g.concat(d)}):Promise.resolve(d);L(a,{id:b.id,bound:c})}return a}
function ma(a,b){function d(){var c=Array.prototype.slice.call(arguments);return b(a,c)}L(d,{id:a,bound:null});return d}var M=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function N(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}N.prototype=Object.create(Promise.prototype);
N.prototype.then=function(a,b){switch(this.status){case "resolved_model":O(this);break;case "resolved_module":P(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":case "cyclic":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function na(a){switch(a.status){case "resolved_model":O(a);break;case "resolved_module":P(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":throw a;default:throw a.reason;}}function Q(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}function R(a,b,d){switch(a.status){case "fulfilled":Q(b,a.value);break;case "pending":case "blocked":case "cyclic":a.value=b;a.reason=d;break;case "rejected":d&&Q(d,a.reason)}}
function S(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Q(d,b)}}function T(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.value,c=a.reason;a.status="resolved_module";a.value=b;null!==d&&(P(a),R(a,d,c))}}var U=null,V=null;
function O(a){var b=U,d=V;U=a;V=null;var c=a.value;a.status="cyclic";a.value=null;a.reason=null;try{var g=JSON.parse(c,a._response._fromJSON);if(null!==V&&0<V.deps)V.value=g,a.status="blocked",a.value=null,a.reason=null;else{var h=a.value;a.status="fulfilled";a.value=g;null!==h&&Q(h,g)}}catch(l){a.status="rejected",a.reason=l}finally{U=b,V=d}}
function P(a){try{var b=a.value,d=x.get(b.specifier);if("fulfilled"===d.status)var c=d.value;else throw d.reason;var g="*"===b.name?c:""===b.name?c.default:c[b.name];a.status="fulfilled";a.value=g}catch(h){a.status="rejected",a.reason=h}}function W(a,b){a._chunks.forEach(function(d){"pending"===d.status&&S(d,b)})}function X(a,b){var d=a._chunks,c=d.get(b);c||(c=new N("pending",null,null,a),d.set(b,c));return c}
function oa(a,b,d,c){if(V){var g=V;c||g.deps++}else g=V={deps:c?0:1,value:null};return function(h){b[d]=h;g.deps--;0===g.deps&&"blocked"===a.status&&(h=a.value,a.status="fulfilled",a.value=g.value,null!==h&&Q(h,g.value))}}function pa(a){return function(b){return S(a,b)}}
function qa(a,b){function d(){var g=Array.prototype.slice.call(arguments),h=b.bound;return h?"fulfilled"===h.status?c(b.id,h.value.concat(g)):Promise.resolve(h).then(function(l){return c(b.id,l.concat(g))}):c(b.id,g)}var c=a._callServer;L(d,b);return d}function Y(a,b){a=X(a,b);switch(a.status){case "resolved_model":O(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ra(a,b,d,c){if("$"===c[0]){if("$"===c)return B;switch(c[1]){case "$":return c.slice(1);case "L":return b=parseInt(c.slice(2),16),a=X(a,b),{$$typeof:F,_payload:a,_init:na};case "@":return b=parseInt(c.slice(2),16),X(a,b);case "S":return Symbol.for(c.slice(2));case "P":return a=c.slice(2),M[a]||(b={$$typeof:D,_currentValue:G,_currentValue2:G,_defaultValue:G,_threadCount:0,Provider:null,Consumer:null,_globalName:a},b.Provider={$$typeof:C,_context:b},M[a]=b),M[a].Provider;case "F":return b=parseInt(c.slice(2),
16),b=Y(a,b),qa(a,b);case "Q":return b=parseInt(c.slice(2),16),a=Y(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Y(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=X(a,c);switch(a.status){case "resolved_model":O(a);break;case "resolved_module":P(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":return c=
U,a.then(oa(c,b,d,"cyclic"===a.status),pa(c)),null;default:throw a.reason;}}}return c}function sa(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function ta(a,b,d,c){var g=new Map;a={_bundlerConfig:a,_moduleLoading:b,_callServer:void 0!==d?d:sa,_nonce:c,_chunks:g,_stringDecoder:new p.TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=ua(a);return a}
function va(a,b,d){var c=a._chunks,g=c.get(b);d=JSON.parse(d,a._fromJSON);var h=w(a._bundlerConfig,d);z(a._moduleLoading,d[1],a._nonce);if(d=y(h)){if(g){var l=g;l.status="blocked"}else l=new N("blocked",null,null,a),c.set(b,l);d.then(function(){return T(l,h)},function(k){return S(l,k)})}else g?T(g,h):c.set(b,new N("resolved_module",h,null,a))}
function ua(a){return function(b,d){return"string"===typeof d?ra(a,this,b,d):"object"===typeof d&&null!==d?(b=d[0]===B?{$$typeof:B,type:d[1],key:d[2],ref:null,props:d[3],_owner:null}:d,b):d}}function Z(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
exports.createFromNodeStream=function(a,b,d){var c=ta(b.moduleMap,b.moduleLoading,Z,d&&"string"===typeof d.nonce?d.nonce:void 0);a.on("data",function(g){for(var h=0,l=c._rowState,k=c._rowID,e=c._rowTag,f=c._rowLength,t=c._buffer,E=g.length;h<E;){var m=-1;switch(l){case 0:m=g[h++];58===m?l=1:k=k<<4|(96<m?m-87:m-48);continue;case 1:l=g[h];84===l?(e=l,l=2,h++):64<l&&91>l?(e=l,l=3,h++):(e=0,l=3);continue;case 2:m=g[h++];44===m?l=4:f=f<<4|(96<m?m-87:m-48);continue;case 3:m=g.indexOf(10,h);break;case 4:m=
h+f,m>g.length&&(m=-1)}var n=g.byteOffset+h;if(-1<m){f=new Uint8Array(g.buffer,n,m-h);h=e;n=c._stringDecoder;e="";for(var q=0;q<t.length;q++)e+=n.decode(t[q],v);e+=n.decode(f);switch(h){case 73:va(c,k,e);break;case 72:k=e[0];e=e.slice(1);e=JSON.parse(e,c._fromJSON);if(f=A.current)switch(k){case "D":f.prefetchDNS(e);break;case "C":"string"===typeof e?f.preconnect(e):f.preconnect(e[0],e[1]);break;case "L":k=e[0];h=e[1];3===e.length?f.preload(k,h,e[2]):f.preload(k,h);break;case "m":"string"===typeof e?
f.preloadModule(e):f.preloadModule(e[0],e[1]);break;case "S":"string"===typeof e?f.preinitStyle(e):f.preinitStyle(e[0],0===e[1]?void 0:e[1],3===e.length?e[2]:void 0);break;case "X":"string"===typeof e?f.preinitScript(e):f.preinitScript(e[0],e[1]);break;case "M":"string"===typeof e?f.preinitModuleScript(e):f.preinitModuleScript(e[0],e[1])}break;case 69:e=JSON.parse(e);f=e.digest;e=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
e.stack="Error: "+e.message;e.digest=f;f=c._chunks;(h=f.get(k))?S(h,e):f.set(k,new N("rejected",null,e,c));break;case 84:c._chunks.set(k,new N("fulfilled",e,null,c));break;default:f=c._chunks,(h=f.get(k))?(k=h,"pending"===k.status&&(f=k.value,h=k.reason,k.status="resolved_model",k.value=e,null!==f&&(O(k),R(k,f,h)))):f.set(k,new N("resolved_model",e,null,c))}h=m;3===l&&h++;f=k=e=l=0;t.length=0}else{g=new Uint8Array(g.buffer,n,g.byteLength-h);t.push(g);f-=g.byteLength;break}}c._rowState=l;c._rowID=
k;c._rowTag=e;c._rowLength=f});a.on("error",function(g){W(c,g)});a.on("end",function(){W(c,Error("Connection closed."))});return X(c,0)};exports.createServerReference=function(a){return ma(a,Z)};

//# sourceMappingURL=react-server-dom-webpack-client.node.unbundled.production.min.js.map
