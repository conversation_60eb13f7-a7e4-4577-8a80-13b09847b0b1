/*
 React
 react-server-dom-turbopack-server.node.unbundled.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("util");require("crypto");var ba=require("async_hooks"),ca=require("react"),da=require("react-dom"),l=null,m=0,p=!0;function q(a,b){a=a.write(b);p=p&&a}
function r(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<m&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),q(a,ea.encode(b));else{var d=l;0<m&&(d=l.subarray(m));d=ea.encodeInto(b,d);var c=d.read;m+=d.written;c<b.length&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=ea.encodeInto(b.slice(c),l).written);2048===m&&(q(a,l),l=new Uint8Array(2048),m=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<m&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),q(a,b)):(d=l.length-m,d<b.byteLength&&
(0===d?q(a,l):(l.set(b.subarray(0,d),m),m+=d,q(a,l),b=b.subarray(d)),l=new Uint8Array(2048),m=0),l.set(b,m),m+=b.byteLength,2048===m&&(q(a,l),l=new Uint8Array(2048),m=0)));return p}var ea=new aa.TextEncoder,t=Symbol.for("react.client.reference"),v=Symbol.for("react.server.reference");function x(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:b},$$async:{value:d}})}var fa=Function.prototype.bind,ha=Array.prototype.slice;
function ia(){var a=fa.apply(this,arguments);if(this.$$typeof===v){var b=ha.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:v},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:ia}})}return a}
var ja=Promise.prototype,ka={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}},la={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];
case "__esModule":var d=a.$$id;a.default=x(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=x({},a.$$id,!0),e=new Proxy(c,la);a.status="fulfilled";a.value=e;return a.then=x(function(f){return Promise.resolve(f(e))},
a.$$id+"#then",!1)}c=a[b];c||(c=x(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ka));return c},getPrototypeOf:function(){return ja},set:function(){throw Error("Cannot assign to a client module from a server module.");
}},ta={prefetchDNS:ma,preconnect:na,preload:oa,preloadModule:pa,preinitStyle:qa,preinitScript:ra,preinitModuleScript:sa};function ma(a){if("string"===typeof a&&a){var b=y();if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),z(b,"D",a))}}}function na(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?z(d,"C",[a,b]):z(d,"C",a))}}}
function oa(a,b,d){if("string"===typeof a){var c=y();if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,k=d.imageSizes,h="";"string"===typeof g&&""!==g?(h+="["+g+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;f+="[image]"+h}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=A(d))?z(c,"L",[a,b,d]):z(c,"L",[a,b]))}}}function pa(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=A(b))?z(d,"m",[a,b]):z(d,"m",a)}}}
function qa(a,b,d){if("string"===typeof a){var c=y();if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=A(d))?z(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?z(c,"S",[a,b]):z(c,"S",a)}}}function ra(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=A(b))?z(d,"X",[a,b]):z(d,"X",a)}}}function sa(a,b){if("string"===typeof a){var d=y();if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=A(b))?z(d,"M",[a,b]):z(d,"M",a)}}}
function A(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var ua=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,va=new ba.AsyncLocalStorage,B=Symbol.for("react.element"),wa=Symbol.for("react.fragment"),xa=Symbol.for("react.provider"),ya=Symbol.for("react.server_context"),za=Symbol.for("react.forward_ref"),Aa=Symbol.for("react.suspense"),Ba=Symbol.for("react.suspense_list"),Ca=Symbol.for("react.memo"),C=Symbol.for("react.lazy"),Da=Symbol.for("react.default_value"),Ea=Symbol.for("react.memo_cache_sentinel"),Fa=Symbol.for("react.postpone"),
Ga=Symbol.iterator,D=null;function Ha(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Ha(a,d);b.context._currentValue=b.value}}}function Ia(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ia(a)}
function Ja(a){var b=a.parent;null!==b&&Ja(b);a.context._currentValue=a.value}function Ka(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Ha(a,b):Ka(a,b)}
function La(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?Ha(a,d):La(a,d);b.context._currentValue=b.value}function Ma(a){var b=D;b!==a&&(null===b?Ja(a):null===a?Ia(b):b.depth===a.depth?Ha(b,a):b.depth>a.depth?Ka(b,a):La(b,a),D=a)}function Na(a,b){var d=a._currentValue;a._currentValue=b;var c=D;return D=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var Oa=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Pa(){}function Qa(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Pa,Pa),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Ra=b;throw Oa;}}var Ra=null;
function Sa(){if(null===Ra)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Ra;Ra=null;return a}var E=null,Ta=0,F=null;function Ua(){var a=F;F=null;return a}function Va(a){return a._currentValue}
var Za={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:G,useTransition:G,readContext:Va,useContext:Va,useReducer:G,useRef:G,useState:G,useInsertionEffect:G,useLayoutEffect:G,useImperativeHandle:G,useEffect:G,useId:Wa,useSyncExternalStore:G,useCacheRefresh:function(){return Xa},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Ea;return b},use:Ya};
function G(){throw Error("This Hook is not supported in Server Components.");}function Xa(){throw Error("Refreshing the cache is not supported in Server Components.");}function Wa(){if(null===E)throw Error("useId can only be used while React is rendering");var a=E.identifierCount++;return":"+E.identifierPrefix+"S"+a.toString(32)+":"}
function Ya(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Ta;Ta+=1;null===F&&(F=[]);return Qa(F,a,b)}if(a.$$typeof===ya)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function $a(){return(new AbortController).signal}function ab(){var a=y();return a?a.cache:new Map}
var bb={getCacheSignal:function(){var a=ab(),b=a.get($a);void 0===b&&(b=$a(),a.set($a,b));return b},getCacheForType:function(a){var b=ab(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},cb=Array.isArray,db=Object.getPrototypeOf;function eb(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function fb(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(cb(a))return"[...]";a=eb(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function gb(a){if("string"===typeof a)return a;switch(a){case Aa:return"Suspense";case Ba:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case za:return gb(a.render);case Ca:return gb(a.type);case C:var b=a._payload;a=a._init;try{return gb(a(b))}catch(d){}}return""}
function I(a,b){var d=eb(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(cb(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?I(g):fb(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===B)e="<"+gb(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var k=f[g],h=JSON.stringify(k);e+=('"'+k+'"'===h?k:h)+": ";h=a[k];h="object"===typeof h&&null!==h?I(h):
fb(h);k===b?(d=e.length,c=h.length,e+=h):e=10>h.length&&40>e.length+h.length?e+h:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var hb=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ib=hb.ContextRegistry,J=ca.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!J)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var jb=Object.prototype,K=JSON.stringify,kb=J.TaintRegistryObjects,L=J.TaintRegistryValues,lb=J.TaintRegistryByteLengths,mb=J.TaintRegistryPendingRequests,nb=J.ReactCurrentCache,ob=hb.ReactCurrentDispatcher;function M(a){throw Error(a);}
function pb(a){a=a.taintCleanupQueue;mb.delete(a);for(var b=0;b<a.length;b++){var d=a[b],c=L.get(d);void 0!==c&&(1===c.count?L.delete(d):c.count--)}a.length=0}function qb(a){console.error(a)}function rb(){}
function sb(a,b,d,c,e,f){if(null!==nb.current&&nb.current!==bb)throw Error("Currently React only supports one RSC renderer at a time.");ua.current=ta;nb.current=bb;var g=new Set,k=[],h=[];mb.add(h);var n=new Set,w={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:n,abortableTasks:g,pingedTasks:k,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:h,onError:void 0===d?qb:d,onPostpone:void 0===f?rb:f,toJSON:function(u,H){return tb(w,this,u,H)}};w.pendingChunks++;b=ub(c);a=vb(w,a,b,g);k.push(a);return w}var N=null;function y(){if(N)return N;var a=va.getStore();return a?a:null}var wb={};
function xb(a,b){a.pendingChunks++;var d=vb(a,null,D,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,yb(a,d),d.id;case "rejected":var c=b.reason;"object"===typeof c&&null!==c&&c.$$typeof===Fa?(zb(a,c.message),Ab(a,d.id)):(c=O(a,c),P(a,d.id,c));return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=
e;yb(a,d)},function(e){d.status=4;a.abortableTasks.delete(d);e=O(a,e);P(a,d.id,e);null!==a.destination&&Q(a,a.destination)});return d.id}function z(a,b,d){d=K(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;a.completedHintChunks.push(b+d+"\n");Bb(a)}function Cb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function Db(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:C,_payload:a,_init:Cb}}
function R(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===t)return[B,b,d,e];Ta=0;F=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:Db(e):e}if("string"===typeof b)return[B,b,d,e];if("symbol"===typeof b)return b===wa?e.children:[B,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===t)return[B,b,d,e];switch(b.$$typeof){case C:var g=
b._init;b=g(b._payload);return R(a,b,d,c,e,f);case za:return a=b.render,Ta=0,F=f,a(e,void 0);case Ca:return R(a,b.type,d,c,e,f);case xa:return Na(b._context,e.value),[B,b,d,{value:e.value,children:e.children,__pop:wb}]}}throw Error("Unsupported Server Component type: "+fb(b));}function yb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return Eb(a)}))}
function vb(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return yb(a,e)},thenableState:null};c.add(e);return e}function S(a){return"$"+a.toString(16)}function Fb(a,b,d){a=K(d);return b.toString(16)+":"+a+"\n"}
function Gb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===B&&"1"===d?"$L"+g.toString(16):S(g);try{var k=a.bundlerConfig,h=c.$$id;g="";var n=k[h];if(n)g=n.name;else{var w=h.lastIndexOf("#");-1!==w&&(g=h.slice(w+1),n=k[h.slice(0,w)]);if(!n)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var u=!0===c.$$async?[n.id,n.chunks,g,1]:[n.id,n.chunks,
g];a.pendingChunks++;var H=a.nextChunkId++,Zb=K(u),$b=H.toString(16)+":I"+Zb+"\n";a.completedImportChunks.push($b);f.set(e,H);return b[0]===B&&"1"===d?"$L"+H.toString(16):S(H)}catch(ac){return a.pendingChunks++,b=a.nextChunkId++,d=O(a,ac),P(a,b,d),S(b)}}function T(a,b){a.pendingChunks++;b=vb(a,b,D,a.abortableTasks);Hb(a,b);return b.id}
function U(a,b,d){if(lb.has(d.byteLength)){var c=L.get(String.fromCharCode.apply(String,new Uint8Array(d.buffer,d.byteOffset,d.byteLength)));void 0!==c&&M(c.message)}a.pendingChunks+=2;c=a.nextChunkId++;d=new Uint8Array(d.buffer,d.byteOffset,d.byteLength);var e=d.byteLength;b=c.toString(16)+":"+b+e.toString(16)+",";a.completedRegularChunks.push(b,d);return S(c)}var V=!1;
function tb(a,b,d,c){switch(c){case B:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===B||c.$$typeof===C);)try{switch(c.$$typeof){case B:var e=a.writtenObjects,f=e.get(c);if(void 0!==f){if(-1===f){var g=T(a,c);return S(g)}if(V===c)V=null;else return S(f)}else e.set(c,-1);var k=c;c=R(a,k.type,k.key,k.ref,k.props,null);break;case C:var h=c._init;c=h(c._payload)}}catch(n){d=n===Oa?Sa():n;if("object"===typeof d&&null!==d){if("function"===typeof d.then)return a.pendingChunks++,a=vb(a,c,D,a.abortableTasks),
c=a.ping,d.then(c,c),a.thenableState=Ua(),"$L"+a.id.toString(16);if(d.$$typeof===Fa)return c=d,a.pendingChunks++,d=a.nextChunkId++,zb(a,c.message),Ab(a,d),"$L"+d.toString(16)}a.pendingChunks++;c=a.nextChunkId++;d=O(a,d);P(a,c,d);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){e=kb.get(c);void 0!==e&&M(e);if(c.$$typeof===t)return Gb(a,b,d,c);b=a.writtenObjects;e=b.get(c);if("function"===typeof c.then){if(void 0!==e)if(V===c)V=null;else return"$@"+e.toString(16);a=xb(a,c);
b.set(c,a);return"$@"+a.toString(16)}if(c.$$typeof===xa)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=Fb(a,d,"$P"+c),a.completedRegularChunks.push(c)),S(d);if(c===wb){a=D;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===Da?a.context._defaultValue:c;D=a.parent;return}if(void 0!==e){if(-1===e)return a=T(a,c),S(a);if(V===c)V=null;
else return S(e)}else b.set(c,-1);if(cb(c))return c;if(c instanceof Map){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d][0],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$Q"+T(a,c).toString(16)}if(c instanceof Set){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$W"+T(a,c).toString(16)}if(c instanceof ArrayBuffer)return U(a,"A",new Uint8Array(c));if(c instanceof Int8Array)return U(a,
"C",c);if(c instanceof Uint8Array)return U(a,"c",c);if(c instanceof Uint8ClampedArray)return U(a,"U",c);if(c instanceof Int16Array)return U(a,"S",c);if(c instanceof Uint16Array)return U(a,"s",c);if(c instanceof Int32Array)return U(a,"L",c);if(c instanceof Uint32Array)return U(a,"l",c);if(c instanceof Float32Array)return U(a,"F",c);if(c instanceof Float64Array)return U(a,"D",c);if(c instanceof BigInt64Array)return U(a,"N",c);if(c instanceof BigUint64Array)return U(a,"m",c);if(c instanceof DataView)return U(a,
"V",c);null===c||"object"!==typeof c?a=null:(a=Ga&&c[Ga]||c["@@iterator"],a="function"===typeof a?a:null);if(a)return Array.from(c);a=db(c);if(a!==jb&&(null===a||null!==db(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return c}if("string"===typeof c){e=L.get(c);void 0!==e&&M(e.message);if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=
2,d=a.nextChunkId++,b="string"===typeof c?Buffer.byteLength(c,"utf8"):c.byteLength,b=d.toString(16)+":T"+b.toString(16)+",",a.completedRegularChunks.push(b,c),S(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){e=kb.get(c);void 0!==e&&M(e);if(c.$$typeof===t)return Gb(a,b,
d,c);if(c.$$typeof===v)return d=a.writtenServerReferences,b=d.get(c),void 0!==b?a="$F"+b.toString(16):(b=c.$$bound,b={id:c.$$id,bound:b?Promise.resolve(b):null},a=T(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+I(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+
I(b,d));}if("symbol"===typeof c){e=a.writtenSymbols;f=e.get(c);if(void 0!==f)return S(f);f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+I(b,d));a.pendingChunks++;d=a.nextChunkId++;b=Fb(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return S(d)}if("bigint"===typeof c)return a=L.get(c),void 0!==a&&M(a.message),"$n"+c.toString(10);
throw Error("Type "+typeof c+" is not supported in Client Component props."+I(b,d));}function zb(a,b){a=a.onPostpone;a(b)}function O(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}
function Ib(a,b){pb(a);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}function Ab(a,b){b=b.toString(16)+":P\n";a.completedErrorChunks.push(b)}function P(a,b,d){d={digest:d};b=b.toString(16)+":E"+K(d)+"\n";a.completedErrorChunks.push(b)}
function Hb(a,b){if(0===b.status){Ma(b.context);try{var d=b.model;if("object"===typeof d&&null!==d&&d.$$typeof===B){a.writtenObjects.set(d,b.id);var c=d,e=b.thenableState;b.model=d;d=R(a,c.type,c.key,c.ref,c.props,e);for(b.thenableState=null;"object"===typeof d&&null!==d&&d.$$typeof===B;)a.writtenObjects.set(d,b.id),c=d,b.model=d,d=R(a,c.type,c.key,c.ref,c.props,null)}"object"===typeof d&&null!==d&&a.writtenObjects.set(d,b.id);var f=b.id;V=d;var g=K(d,a.toJSON),k=f.toString(16)+":"+g+"\n";a.completedRegularChunks.push(k);
a.abortableTasks.delete(b);b.status=1}catch(h){f=h===Oa?Sa():h;if("object"===typeof f&&null!==f){if("function"===typeof f.then){a=b.ping;f.then(a,a);b.thenableState=Ua();return}if(f.$$typeof===Fa){a.abortableTasks.delete(b);b.status=4;zb(a,f.message);Ab(a,b.id);return}}a.abortableTasks.delete(b);b.status=4;f=O(a,f);P(a,b.id,f)}}}
function Eb(a){var b=ob.current;ob.current=Za;var d=N;E=N=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++)Hb(a,c[e]);null!==a.destination&&Q(a,a.destination)}catch(f){O(a,f),Ib(a,f)}finally{ob.current=b,E=null,N=d}}
function Q(a,b){l=new Uint8Array(2048);m=0;p=!0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)if(a.pendingChunks--,!r(b,d[c])){a.destination=null;c++;break}d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)if(!r(b,e[c])){a.destination=null;c++;break}e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)if(a.pendingChunks--,!r(b,f[c])){a.destination=null;c++;break}f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)if(a.pendingChunks--,!r(b,g[c])){a.destination=
null;c++;break}g.splice(0,c)}finally{a.flushScheduled=!1,l&&0<m&&b.write(l.subarray(0,m)),l=null,m=0,p=!0}"function"===typeof b.flush&&b.flush();0===a.pendingChunks&&(pb(a),b.end())}function Jb(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return va.run(a,Eb,a)})}function Bb(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setImmediate(function(){return Q(a,b)})}}
function Kb(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{Q(a,b)}catch(d){O(a,d),Ib(a,d)}}}
function Lb(a,b){try{var d=a.abortableTasks;if(0<d.size){a.pendingChunks++;var c=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===Fa)zb(a,b.message),Ab(a,c,b);else{var e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=O(a,e);P(a,c,f,e)}d.forEach(function(g){g.status=3;var k=S(c);g=Fb(a,g.id,k);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&Q(a,a.destination)}catch(g){O(a,g),Ib(a,g)}}
function ub(a){if(a){var b=D;Ma(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];if(!ib[e]){var f={$$typeof:ya,_currentValue:Da,_currentValue2:Da,_defaultValue:Da,_threadCount:0,Provider:null,Consumer:null,_globalName:e};f.Provider={$$typeof:xa,_context:f};ib[e]=f}Na(ib[e],c)}a=D;Ma(b);return a}return null}function Mb(a,b){var d=b.lastIndexOf("#");a=b.slice(0,d);b=b.slice(d+1);return{specifier:a,name:b}}var Nb=new Map;
function Ob(a){var b=Nb.get(a.specifier);if(b)return"fulfilled"===b.status?null:b;var d=import(a.specifier);a.async&&(d=d.then(function(c){return c.default}));d.then(function(c){var e=d;e.status="fulfilled";e.value=c},function(c){var e=d;e.status="rejected";e.reason=c});Nb.set(a.specifier,d);return d}function W(a){var b=Nb.get(a.specifier);if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a.name?b:""===a.name?b.default:b[a.name]}
function Pb(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}Pb.prototype=Object.create(Promise.prototype);Pb.prototype.then=function(a,b){switch(this.status){case "resolved_model":Qb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function Rb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}function Sb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Rb(d,b)}}function Tb(a,b,d,c,e,f){var g=Mb(a._bundlerConfig,b);a=Ob(g);if(d)d=Promise.all([d,a]).then(function(k){k=k[0];var h=W(g);return h.bind.apply(h,[null].concat(k))});else if(a)d=Promise.resolve(a).then(function(){return W(g)});else return W(g);d.then(Ub(c,e,f),Vb(c));return null}var X=null,Y=null;
function Qb(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Wb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Sb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new Pb("resolved_model",c,null,a):new Pb("pending",null,null,a),d.set(b,c));return c}function Ub(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Rb(e,c.value))}}function Vb(a){return function(b){return Sb(a,b)}}
function Xb(a,b){a=Z(a,b);"resolved_model"===a.status&&Qb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Yb(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Xb(a,c),Tb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Xb(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Xb(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,k){k.startsWith(e)&&f.append(k.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Qb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Ub(c,b,d),Vb(c)),null;default:throw a.reason;}}return c}
function bc(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?Yb(e,this,f,g):g}};return e}
function cc(a,b,d){a._formData.append(b,d);var c=a._prefix;if(b.startsWith(c)&&(a=a._chunks,b=+b.slice(c.length),(b=a.get(b))&&"pending"===b.status&&(c=b.value,a=b.reason,b.status="resolved_model",b.value=d,null!==c)))switch(Qb(b),b.status){case "fulfilled":Rb(c,b.value);break;case "pending":case "blocked":b.value=c;b.reason=a;break;case "rejected":a&&Rb(a,b.reason)}}function dc(a){Wb(a,Error("Connection closed."))}
function ec(a,b,d){var c=Mb(a,b);a=Ob(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=W(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return W(c)}):Promise.resolve(W(c))}function fc(a,b,d){a=bc(b,d,a);dc(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}function gc(a,b){return function(){return Kb(b,a)}}exports.createClientModuleProxy=function(a){a=x({},a,!1);return new Proxy(a,la)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=fc(a,b,e),c=ec(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=ec(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=bc(b,"",a);b=Z(a,0);dc(a);return b};
exports.decodeReplyFromBusboy=function(a,b){var d=bc(b,""),c=0,e=[];a.on("field",function(f,g){0<c?e.push(f,g):cc(d,f,g)});a.on("file",function(f,g,k){var h=k.filename,n=k.mimeType;if("base64"===k.encoding.toLowerCase())throw Error("React doesn't accept base64 encoded file uploads because we don't expect form data passed from a browser to ever encode data that way. If that's the wrong assumption, we can easily fix it.");c++;var w=[];g.on("data",function(u){w.push(u)});g.on("end",function(){var u=
new Blob(w,{type:n});d._formData.append(f,u,h);c--;if(0===c){for(u=0;u<e.length;u+=2)cc(d,e[u],e[u+1]);e.length=0}})});a.on("finish",function(){dc(d)});a.on("error",function(f){Wb(d,f)});return Z(d,0)};exports.registerClientReference=function(a,b,d){return x(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:v},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:ia}})};
exports.renderToPipeableStream=function(a,b,d){var c=sb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0),e=!1;Jb(c);return{pipe:function(f){if(e)throw Error("React currently only supports piping to one writable stream.");e=!0;Kb(c,f);f.on("drain",gc(f,c));return f},abort:function(f){Lb(c,f)}}};

//# sourceMappingURL=react-server-dom-turbopack-server.node.unbundled.production.min.js.map
